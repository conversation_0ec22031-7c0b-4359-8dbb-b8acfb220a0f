{"name": "moxtra", "version": "10.3.0", "description": "moxtra new site", "author": "", "private": true, "sideEffect": false, "scripts": {"lang_dev": "node ./views/i18n/tasks/lang.process.js mep", "ajv_compile": "node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js", "admin_dev": "node ./views/i18n/tasks/lang.process.js mep && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/mep_admin/webpack.dev.js", "mep_dev": "node ./views/i18n/tasks/lang.process.js mep && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=5096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https-cert ../colin.com+5.pem --https-key ../colin.com+5-key.pem --progress --config build/mep_portal/webpack.dev.js", "client_dev": "node ./views/i18n/tasks/lang.process.js mep && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/mep_client/webpack.dev.js", "build_mep": "rimraf dist && node ./views/i18n/tasks/lang.process.js && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=12288 node_modules/webpack/bin/webpack.js --config build/mep_portal/webpack.prod", "build_template": "rimraf dist && node build/email_template/genEmailTpl.js moxo && build/svn.sh templates moxo", "build_local_template": "rimraf dist && node build/email_template/genEmailTpl.js moxo", "build_admin": "rimraf dist && node ./views/i18n/tasks/lang.process.js && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=4096 node_modules/webpack/bin/webpack.js --config build/mep_admin/webpack.prod", "controller_unit": "BABEL_ENV=test jest -c test/unit/controller/jest.conf.js", "watch_unit": "BABEL_ENV=test jest --watchAll -c test/unit/controller/jest.conf.js", "format:standard": "standard --fix", "format:eslint": "eslint --ext .js,.vue --fix", "format": "eslint --ext .js,.vue views --fix && standard 'views/**/*.{js,vue}' --fix", "prepare": "lerna run prepare", "bootstrap": "lerna bootstrap", "build_policy": "rimraf app/mepPortal/policies && webpack --config build/policy/webpack.base", "build_whitelabel": "rimraf dist && node ./views/i18n/tasks/lang.process.js && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=4096 node_modules/webpack/bin/webpack.js --config build/mep_portal/webpack.prod --whitelabel=$name", "dialin_dev": "node ./views/i18n/tasks/lang.process.js mep && node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/dialin/webpack.dev.js", "dialin_build": "rimraf app/mepPortal/dial-in && webpack --config build/dialin/webpack.pro.js", "custom_auth_dev": "node ./views/i18n/tasks/lang.process.js mep && node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/custom_auth/webpack.dev.js", "custom_auth_build": "rimraf app/mepPortal/custom-auth && webpack --config build/custom_auth/webpack.pro.js", "redirect_dev": "node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/redirect/webpack.dev.js", "redirect_build": "rimraf app/mepPortal/redirect && webpack --config build/redirect/webpack.pro.js", "redirect_build_agent": "rimraf dist && webpack --config build/redirect/webpack.agent.js --env type=agent", "scan:meet": "sonar-scanner -Dproject.settings=./sonar-project-meet.properties ", "scan": "sonar-scanner -Dproject.settings=./sonar-project-portal.properties ", "build_whitelabel_template": "rimraf dist && node build/email_template/genEmailTpl.js $name", "build_dll": "rimraf output && webpack --config build/mep_portal/webpack.dll", "build_annotate": "rimraf views/annotate/dist && webpack --config build/custom_library/annotate/webpack.config.js", "forms_dev": "node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https-cert ../colin.com+5.pem --https-key ../colin.com+5-key.pem --progress --config build/forms/webpack.dev.js", "build_forms": "rimraf app/form && node ./views/i18n/tasks/lang.process.js && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=8192 node_modules/webpack/bin/webpack.js --config build/forms/webpack.prod", "getstarted_dev": "node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/getstarted/webpack.dev.js", "build_getstarted": "rimraf dist && node ./views/i18n/tasks/lang.process.js && node ./views/contentLibrary/plugins/form/tasks/compile_schemas.js && node --max_old_space_size=12288 node_modules/webpack/bin/webpack.js --config build/getstarted/webpack.prod", "jwt_redirect_dev": "node --max_old_space_size=4096 node_modules/webpack-dev-server/bin/webpack-dev-server.js --https --progress --config build/jwt_redirect/webpack.dev.js", "build_jwt_redirect": "rimraf app/jwt-redirect && node ./views/i18n/tasks/lang.process.js && node --max_old_space_size=12288 node_modules/webpack/bin/webpack.js --config build/jwt_redirect/webpack.prod"}, "dependencies": {"@simonwep/pickr": "1.8.2", "@stripe/stripe-js": "^1.3.2", "@svgdotjs/svg.js": "3.2.0", "@tinymce/tinymce-vue": "^3.2.8", "@types/moment-timezone": "^0.5.12", "@types/vue": "^2.0.0", "@vue/composition-api": "1.7.2", "async-validator": "~1.8.1", "clamp-js": "^0.7.0", "crypto-js": "^4.1.1", "deepmerge": "^1.2.0", "dexie": "3.0.3", "draggabilly": "2.2.0", "echarts": "^5.5.0", "hammerjs": "2.0.8", "imagesloaded": "4.1.4", "json2csv": "^4.5.1", "jszip": "^3.10.1", "lodash": "4.17.21", "markdown-it": "^13.0.1", "markdown-it-sanitizer": "^0.4.3", "mime": "^4.0.4", "moment-duration-format": "2.2.2", "moment-timezone": "^0.5.26", "normalize-wheel": "^1.0.1", "pdfjs-dist": "^2.2.228", "pinia": "~2.1.6", "qrcode": "^1.5.3", "resize-observer-polyfill": "^1.5.0", "rrule": "2.6.4", "sass": "^1.32.13", "sass-loader": "^12.6.0", "screenfull": "4.2.0", "scrollparent": "^2.0.1", "sortablejs": "1.9.0", "throttle-debounce": "^1.0.1", "tinymce": "^7.7.2", "ua-parser-js": "^0.7.39", "video.js": "^8.21.0", "vue": "2.6.11", "vue-cropperjs": "4.2.0", "vue-draggable-resizable": "^2.3.0", "vue-echarts": "^6.6.9", "vue-fullscreen": "2.1.5", "vue-i18n": "^7.8.1", "vue-i18n-composable": "^0.2.2", "vue-jsonp": "^2.0.0", "vue-lazyload": "^1.3.3", "vue-multiple-progress": "1.6.0", "vue-observe-visibility": "^0.4.4", "vue-plyr": "^5.1.3", "vue-resize": "^0.4.5", "vue-router": "3.0.1", "vue-smooth-dnd": "^0.8.0", "vue-virtual-scroll-list": "1.2.8", "vuex": "3.6.2"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-proposal-optional-chaining": "7.12.7", "@babel/plugin-syntax-dynamic-import": "^7.7.4", "@babel/plugin-syntax-jsx": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.4", "@babel/plugin-transform-object-assign": "^7.7.4", "@babel/plugin-transform-runtime": "^7.7.4", "@babel/plugin-transform-typescript": "^7.7.4", "@babel/polyfill": "^7.7.0", "@babel/preset-env": "^7.7.4", "@babel/register": "^7.7.4", "@babel/runtime": "^7.7.4", "@nuxt/friendly-errors-webpack-plugin": "^2.5.0", "@types/chrome": "0.0.35", "@types/draggabilly": "^2.1.3", "@types/jest": "^29.0.0", "@types/ua-parser-js": "^0.7.33", "@typescript-eslint/eslint-plugin": "^2.5.0", "@typescript-eslint/parser": "^2.5.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0", "@vue/babel-preset-jsx": "^1.4.0", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/eslint-config-typescript": "^10.0.0", "@vue/test-utils": "^1.0.0-beta.29", "ajv": "^8.11.0", "ajv-errors": "^3.0.0", "autoprefixer": "6.7.7", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.3", "babel-loader": "^8.0.6", "backbone": "1.1.0", "canvas": "^2.6.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chalk": "^2.4.2", "clean-webpack-plugin": "^4.0.0", "colors": "^1.3.3", "compression-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^9.1.0", "cross-spawn": "^5.0.1", "css-loader": "^3.2.1", "eslint": "^6.7.2", "eslint-config-prettier": "^3.1.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^3.0.2", "eslint-plugin-html": "^4.0.6", "eslint-plugin-import": "^2.13.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^8.0.1", "file-loader": "^6.2.0", "happypack": "^5.0.1", "html-webpack-plugin": "5.5.0", "html-webpack-tags-plugin": "3.0.2", "jest": "^29.0.0", "jest-environment-jsdom": "^30.0.4", "lerna": "^3.1.1", "mini-css-extract-plugin": "^2.5.2", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^6.0.1", "ora": "^1.2.0", "portfinder": "^1.0.17", "postcss-import": "^11.0.0", "postcss-loader": "^3.0.0", "postcss-url": "^7.2.1", "prettier": "^1.14.3", "progress-bar-webpack-plugin": "^2.1.0", "resolve-url-loader": "^3.1.1", "rimraf": "^2.6.0", "semver": "^5.5.1", "shelljs": "^0.7.6", "signature_pad": "2.3.2", "sonar-scanner": "^3.1.0", "spin.js": "^4.0.0", "standard": "^12.0.1", "style-loader": "^1.2.1", "svg-sprite-loader": "^6.0.0", "svn-info": "^1.0.0", "terser-webpack-plugin": "^5.3.0", "thread-loader": "3.0.4", "ts-jest": "^29.3.2", "ts-loader": "9.3.1", "tslint": "^6.1.3", "typescript": "4.7.4", "url-loader": "^4.1.1", "vue-eslint-parser": "^7.0.0", "vue-loader": "^15.7.2", "vue-style-loader": "^4.1.2", "vue-template-compiler": "2.6.11", "vuedraggable": "2.21.0", "webpack": "^5.66.0", "webpack-bundle-analyzer": "^3.6.0", "webpack-cli": "^4.9.1", "webpack-deep-scope-plugin": "^1.7.0", "webpack-dev-server": "^4.7.3", "webpack-merge": "^5.8.0", "webpack-post-compile-plugin": "^1.0.0", "worker-loader": "^3.0.8", "xss": "^1.0.11"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "resolutions": {"lodash": "4.17.21"}, "browserify": {"transform": [["babe<PERSON>", {"presets": ["es2015"]}]]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "workspaces": ["views/*", "iSDK", "meetSDK", "mediaSDK"], "standard": {"plugins": ["vue", "html"], "parser": "babel-es<PERSON>"}, "packageManager": "yarn@4.9.0+sha512.5f5f00843eae735dfab6f0442524603e987ceca55e98b36eb0120f4e58908e5b1406545321e46627dca97d15d562f23dc13fb96cabd4e6bc92d379f619001e4e"}