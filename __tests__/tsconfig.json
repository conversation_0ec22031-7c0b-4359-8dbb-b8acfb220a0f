{"compilerOptions": {"target": "es2016", "module": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "isolatedModules": true, "baseUrl": "../../../../", "paths": {"@model/*": ["newController/model/*"], "@newController/*": ["newController/controller/*"], "@controller/*": ["controller/*"], "@commonUtils/*": ["commonUtils/*"], "@commonUtils": ["commonUtils"], "@views/*": ["views/*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules"]}