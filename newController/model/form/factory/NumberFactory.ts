import {
  Form<PERSON><PERSON>e<PERSON><PERSON>ueOption,
  FormConditionElementFactory,
  FormElementCreateOption,
  FormElementFactory, FormElementTransformOption,
  FormUIOption, FormValidateOptions
} from '../defines/shared'
import {isDefine, processLangResource, removeDDRSource} from './shared'
import uuid from 'uuid/v4'

import { FormField } from '@model/form/defines/serverDataStructure'
import pick from 'lodash/pick'
import { conditionViewModelToServerData, fieldConditionToConditionViewModel } from '@model/form/transform/conditions'
import {
  FormElementNumberModel,
  NumberDefaultProps,
  NumberSpecialKeys
} from '@model/form/defines/FormNumber'
import { FormErrorType } from '@model/form/defines/base'
import cloneDeep from 'lodash/cloneDeep'
import type { FormElementAddressModel } from '@model/form/defines/FormAddress'
import { ConditionRuleViewModel, ElementConditionViewModel } from '@model/form/defines/condition'
import { isNumberValueMeetExpected } from '@model/form/common/condition'
import { syncFormElementSampleValue } from '@model/form/factory/utils'
import { getFieldSpecificFormViewModel } from '@model/form/transform/common'
import { removeDDRSource } from './shared'


class NumberFactory implements FormElementFactory<FormElementNumberModel, FormField, number>, FormConditionElementFactory<FormElementNumberModel> {
  create (option: FormElementCreateOption): FormElementNumberModel {
    const result = {
      ...cloneDeep(NumberDefaultProps),
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result
  }

  toServerData (model: FormElementNumberModel): FormField {
    return {
      id: model.id,
      type: model.type,
      label: model.label,
      value: model.value,
      name: model.name,
      defaultValue: model.defaultValue,
      fieldSpecific: getFieldSpecificFormViewModel(model, NumberSpecialKeys)
    } as FormField
  }

  toViewModel (data: FormField, option?: FormElementTransformOption): FormElementNumberModel {
    let value = data.value
    if (option?.applyDefaultValue && data.defaultValue) {
      value = data.defaultValue
    }
    return {
      ...cloneDeep(NumberDefaultProps),
      id: data.id,
      label: data.label,
      name: data.name,
      value,
      defaultValue: data.defaultValue,
      ...pick(data.fieldSpecific, NumberSpecialKeys),
    } as FormElementNumberModel
  }

  validate (model: FormElementNumberModel, option?:FormValidateOptions): Promise<boolean> {
    model.errors = []
    const isTemplate = option?.isValidateTemplate
    let validateValue = isTemplate? model.defaultValue :  model.value
    if(option?.enableDDR) {
      validateValue = removeDDRSource(validateValue)
    }

    if (model.required) {
      if (!isDefine(validateValue) || !validateValue) {
        model.errors.push({
          field: '',
          errorType: FormErrorType.Required
        })
      }
    }

    // Validate minLength and maxLength constraints
    if (validateValue !== null && validateValue !== undefined) {
      const numValue = parseFloat(validateValue.toString())

      if (model.minLength && !isNaN(numValue)) {
        const minLimit = parseInt(model.minLength, 10)
        if (numValue < minLimit) {
          model.errors.push({
            field: '',
            errorType: FormErrorType.MinLimit,
            params: { minLength: minLimit }
          })
        }
      }

      if (model.maxLength && !isNaN(numValue)) {
        const maxLimit = parseInt(model.maxLength, 10)
        if (numValue > maxLimit) {
          model.errors.push({
            field: '',
            errorType: FormErrorType.MaxLimit,
            params: { maxLength: maxLimit }
          })
        }
      }
    }

    return new Promise((resolve, reject) => {
      if (model.errors.length > 0) {
        return reject(model)
      } else {
        return resolve(true)
      }
    })
  }

  resetValue (model: FormElementNumberModel) {
    model.value = null
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormNumberView',
      editor: 'FormNumber'
    }
  }

  meetExpected (model: FormElementNumberModel, rule: ConditionRuleViewModel): boolean {
    return isNumberValueMeetExpected(rule, model.value)
  }

  setValue (
    model: FormElementNumberModel,
    value: number,
    option: FormChangeValueOption
  ): void {
    model.value = value
  }

  syncValue (
    model: FormElementNumberModel,
    value: number,
    option: FormChangeValueOption
  ): void {
    syncFormElementSampleValue(model, value, option)
  }
}

export {
  NumberFactory
}