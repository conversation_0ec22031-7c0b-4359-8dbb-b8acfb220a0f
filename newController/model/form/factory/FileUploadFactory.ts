import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ueOption,
  Form<PERSON>lement<PERSON>reateOption,
  FormElementFactory,
  FormElementTransformOption,
  FormElementType,
  FormUIOption
} from '@model/form/defines/shared'
import { FormField } from '@model/form/defines/serverDataStructure'
import uuid from 'uuid/v4'
import { processLangResource } from '@model/form/factory/shared'

import {
  FormElementFileUploadModel,
  FileUploadDefaultProps,
  FileUploadSpecialKeys,
  FileUploadValue, FileUploadDefaultValue
} from '@model/form/defines/FormFileUpload'
import pick from 'lodash/pick'
import {
  conditionViewModelToServerData,
  fieldConditionToConditionViewModel
} from '@model/form/transform/conditions'
import cloneDeep from 'lodash/cloneDeep'
import { setFormElementValue } from '@model/form/factory/utils'
import { getFieldSpecificFormViewModel } from '@model/form/transform/common'

class FileUploadFactory
  implements FormElementFactory<FormElementFileUploadModel, Form<PERSON>ield, FileUploadValue[]> {
  create (option: FormElementCreateOption): FormElementFileUploadModel {
    const result: FormElementFileUploadModel = {
      ...cloneDeep(FileUploadDefaultProps),
      type: FormElementType.FileUpload,
      id: uuid()
    }
    if (option.$t) {
      processLangResource(result, option.$t)
    }
    return result as FormElementFileUploadModel
  }

  toServerData (model: FormElementFileUploadModel): FormField {
    return {
      id: model.id,
      type: model.type,
      label: model.label,
      value: model.value,
      fieldSpecific: getFieldSpecificFormViewModel(model,FileUploadSpecialKeys)
    } as FormField
  }

  toViewModel (data: FormField, option: FormElementTransformOption): FormElementFileUploadModel {
    let value = data.value
    if (option?.applyDefaultValue && data.defaultValue) {
      value = Array.from(data.defaultValue as FileUploadValue[])
    }
    const model = {
      ...cloneDeep(FileUploadDefaultProps),
      id: data.id,
      label: data.label,
      value,
      defaultValue: data.defaultValue,
      ...pick(data.fieldSpecific, FileUploadSpecialKeys),
    } as FormElementFileUploadModel
    return model
  }

  validate (model: FormElementFileUploadModel): Promise<boolean> {
    return Promise.resolve(true)
  }

  resetValue (model: FormElementFileUploadModel) {
    model.value = []
  }

  uiOption (): FormUIOption {
    return {
      view: 'FormFileUploadView',
      editor: 'FormFileUploadOption'
    }
  }

  setValue (
    model: FormElementFileUploadModel,
    value: FileUploadValue[],
    option: FormChangeValueOption
  ): void {
    model.value = Array.from(value)
  }

  syncValue (
    model: FormElementFileUploadModel,
    value: FileUploadValue[],
    option: FormChangeValueOption
  ): void {
    model.value = Array.from(value)
  }
}

export { FileUploadFactory }
