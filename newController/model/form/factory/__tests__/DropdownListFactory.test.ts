// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { DropdownListFactory } from '../DropdownListFactory'
import {
  DropdownListDefaultProps,
  FormElementDropdownListModel
} from '@model/form/defines/FormDropdownList'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import { ConditionRuleViewModel, ConditionOperatorType } from '@model/form/defines/condition'
import uuid from 'uuid/v4'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock the processLangResource function
jest.mock('../shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

// Mock the isStringValueMeetExpected function
jest.mock('@model/form/common/condition', () => ({
  isStringValueMeetExpected: jest.fn((rule, value) => {
    // Simple mock implementation for testing
    if (rule.operator === ConditionOperatorType.Equal) {
      return value === rule.value
    }
    return false
  })
}))

describe('DropdownListFactory', () => {
  let dropdownListFactory: DropdownListFactory
  let mockDropdownListModel: FormElementDropdownListModel
  let mockFormField: FormField

  beforeEach(() => {
    dropdownListFactory = new DropdownListFactory()

    // Create a mock dropdown list model for testing
    mockDropdownListModel = {
      ...DropdownListDefaultProps,
      id: 'test-id',
      label: 'Test Dropdown',
      value: 'Option 1',
      options: [
        { value: 'Option 1' },
        { value: 'Option 2' }
      ],
      required: true
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'DropdownList',
      label: 'Test Dropdown',
      value: 'Option 1',
      defaultValue: 'Option 2',
      fieldSpecific: {
        options: [
          { value: 'Option 1' },
          { value: 'Option 2' }
        ],
        placeholder: 'Select an option',
        required: true,
        supporting: 'Supporting text',
        hideLabel: false
      }
    }
  })

  describe('create', () => {
    it('should create a new dropdown list model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = dropdownListFactory.create(option)

      expect(result).toEqual({
        ...DropdownListDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = dropdownListFactory.create(option)

      // Check that the label and placeholder have been translated
      expect(result.label).toEqual('translated-label')
      expect(result.placeholder).toEqual('translated-placeholder')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = dropdownListFactory.toServerData(mockDropdownListModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockDropdownListModel.id)
      expect(result.label).toEqual(mockDropdownListModel.label)
      expect(result.value).toEqual(mockDropdownListModel.value)
      expect(result.fieldSpecific.options).toEqual(mockDropdownListModel.options)
      expect(result.fieldSpecific.required).toEqual(mockDropdownListModel.required)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = dropdownListFactory.toViewModel(mockFormField)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.label).toEqual(mockFormField.label)
      expect(result.value).toEqual(mockFormField.value)
      expect(result.options).toEqual(mockFormField.fieldSpecific.options)
      expect(result.required).toEqual(mockFormField.fieldSpecific.required)
      expect(result.placeholder).toEqual(mockFormField.fieldSpecific.placeholder)
      expect(result.supporting).toEqual(mockFormField.fieldSpecific.supporting)
    })

    it('should apply default value when option.applyDefaultValue is true and defaultValue exists', () => {
      const option: FormElementTransformOption = {
        applyDefaultValue: true
      }

      const result = dropdownListFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.value).toEqual(mockFormField.defaultValue)
    })

    it('should handle empty value', () => {
      mockFormField.value = undefined

      const result = dropdownListFactory.toViewModel(mockFormField)

      expect(result.value).toEqual('')
    })
  })

  describe('validate', () => {
    it('should validate successfully when all required fields are filled', async () => {
      const validationPromise = dropdownListFactory.validate(mockDropdownListModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockDropdownListModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockDropdownListModel.value = ''

      const validationPromise = dropdownListFactory.validate(mockDropdownListModel)

      await expect(validationPromise).rejects.toEqual(mockDropdownListModel)
      expect(mockDropdownListModel.errors).toHaveLength(1)
      expect(mockDropdownListModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })

    it('should not validate when isValidateTemplate is true', async () => {
      mockDropdownListModel.value = ''
      const option: FormValidateOptions = {
        isValidateTemplate: true
      }

      const validationPromise = dropdownListFactory.validate(mockDropdownListModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockDropdownListModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty string', () => {
      mockDropdownListModel.value = 'Option 1'

      dropdownListFactory.resetValue(mockDropdownListModel)

      expect(mockDropdownListModel.value).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for normal form', () => {
      const result = dropdownListFactory.uiOption({})

      expect(result).toEqual({
        view: 'FormDropdownListView',
        editor: 'FormDropdownList'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const result = dropdownListFactory.uiOption({ isPDFForm: true })

      expect(result).toEqual({
        view: 'PDFFormDropdownListView',
        editor: 'FormDropdownListSetting'
      })
    })
  })

  describe('meetExpected', () => {
    it('should check if the value meets the expected condition', () => {
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.Equal,
        value: 'Option 1'
      }

      const result = dropdownListFactory.meetExpected(mockDropdownListModel, rule)

      expect(result).toBe(true)
    })

    it('should return false if the value does not meet the expected condition', () => {
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.Equal,
        value: 'Option 2'
      }

      const result = dropdownListFactory.meetExpected(mockDropdownListModel, rule)

      expect(result).toBe(false)
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {}
      
      dropdownListFactory.setValue(mockDropdownListModel, 'Option 2', option)

      expect(mockDropdownListModel.value).toEqual('Option 2')
    })
  })

  describe('syncValue', () => {
    it('should sync the value', () => {
      const option = {}
      
      dropdownListFactory.syncValue(mockDropdownListModel, 'Option 2', option)

      expect(mockDropdownListModel.value).toEqual('Option 2')
    })
  })
})
