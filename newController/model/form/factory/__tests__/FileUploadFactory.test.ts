// Mock problematic modules first


import { FileUploadFactory } from '../FileUploadFactory'
import {
  FileUploadDefaultProps,
  FileUploadSpecialKeys,
  FileUploadValue,
  FormElementFileUploadModel
} from '@model/form/defines/FormFileUpload'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption } from '@model/form/defines/shared'
import uuid from 'uuid/v4'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock the processLangResource function
jest.mock('@model/form/factory/shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

describe('FileUploadFactory', () => {
  let fileUploadFactory: FileUploadFactory
  let mockFileUploadModel: FormElementFileUploadModel
  let mockFormField: FormField
  let mockFileValue: FileUploadValue

  beforeEach(() => {
    fileUploadFactory = new FileUploadFactory()

    // Create a mock file value for testing
    mockFileValue = {
      name: 'test-file.pdf',
      uuid: 'file-uuid-123',
      type: 'application/pdf',
      size: 1024,
      refSeq: 1,
      resSeq: 1,
      url: 'https://example.com/test-file.pdf'
    }

    // Create a mock file upload model for testing
    mockFileUploadModel = {
      ...FileUploadDefaultProps,
      id: 'test-id',
      label: 'Test File Upload',
      value: [mockFileValue],
      maxFileSize: 10,
      fileAccept: '.pdf,.doc,.docx',
      required: true
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'FileUpload',
      label: 'Test File Upload',
      value: [mockFileValue],
      defaultValue: [],
      fieldSpecific: {
        maxFileSize: 10,
        fileAccept: '.pdf,.doc,.docx',
        placeholder: 'Upload files here',
        required: true,
        supporting: 'Supporting text',
        hideLabel: false
      }
    }
  })

  describe('create', () => {
    it('should create a new file upload model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = fileUploadFactory.create(option)

      expect(result).toEqual({
        ...FileUploadDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = fileUploadFactory.create(option)

      // Check that the label has been translated
      expect(result.label).toEqual('translated-label')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = fileUploadFactory.toServerData(mockFileUploadModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFileUploadModel.id)
      expect(result.label).toEqual(mockFileUploadModel.label)
      expect(result.value).toEqual(mockFileUploadModel.value)
      expect(result.fieldSpecific.maxFileSize).toEqual(mockFileUploadModel.maxFileSize)
      expect(result.fieldSpecific.fileAccept).toEqual(mockFileUploadModel.fileAccept)
      expect(result.fieldSpecific.required).toEqual(mockFileUploadModel.required)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = fileUploadFactory.toViewModel(mockFormField, {})

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.label).toEqual(mockFormField.label)
      expect(result.value).toEqual(mockFormField.value)
      expect(result.maxFileSize).toEqual(mockFormField.fieldSpecific.maxFileSize)
      expect(result.fileAccept).toEqual(mockFormField.fieldSpecific.fileAccept)
      expect(result.required).toEqual(mockFormField.fieldSpecific.required)
      expect(result.placeholder).toEqual(mockFormField.fieldSpecific.placeholder)
      expect(result.supporting).toEqual(mockFormField.fieldSpecific.supporting)
    })

    it('should apply default value when option.applyDefaultValue is true and defaultValue exists', () => {
      // Add a default value to the mock form field
      mockFormField.defaultValue = [
        {
          name: 'default-file.pdf',
          uuid: 'default-uuid-123',
          type: 'application/pdf',
          size: 2048,
          refSeq: 2,
          resSeq: 2,
          url: 'https://example.com/default-file.pdf'
        }
      ]

      const option: FormElementTransformOption = {
        applyDefaultValue: true
      }

      const result = fileUploadFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.value).toEqual(mockFormField.defaultValue)
    })
  })

  describe('validate', () => {
    it('should always validate successfully', async () => {
      const validationPromise = fileUploadFactory.validate(mockFileUploadModel)

      await expect(validationPromise).resolves.toBe(true)
    })
  })

  describe('resetValue', () => {
    it('should reset the value to an empty array', () => {
      fileUploadFactory.resetValue(mockFileUploadModel)

      expect(mockFileUploadModel.value).toEqual([])
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = fileUploadFactory.uiOption()

      expect(result).toEqual({
        view: 'FormFileUploadView',
        editor: 'FormFileUploadOption'
      })
    })
  })

  describe('setValue', () => {
    it('should set the value as a new array', () => {
      const newValue: FileUploadValue[] = [
        {
          name: 'new-file.pdf',
          uuid: 'new-uuid-123',
          type: 'application/pdf',
          size: 3072,
          refSeq: 3,
          resSeq: 3,
          url: 'https://example.com/new-file.pdf'
        }
      ]
      const option: FormChangeValueOption = {}
      
      fileUploadFactory.setValue(mockFileUploadModel, newValue, option)

      expect(mockFileUploadModel.value).toEqual(newValue)
      // Ensure it's a new array instance
      expect(mockFileUploadModel.value).not.toBe(newValue)
    })

    it('should handle empty array', () => {
      const option: FormChangeValueOption = {}
      
      fileUploadFactory.setValue(mockFileUploadModel, [], option)

      expect(mockFileUploadModel.value).toEqual([])
    })
  })

  describe('syncValue', () => {
    it('should sync the value as a new array', () => {
      const newValue: FileUploadValue[] = [
        {
          name: 'sync-file.pdf',
          uuid: 'sync-uuid-123',
          type: 'application/pdf',
          size: 4096,
          refSeq: 4,
          resSeq: 4,
          url: 'https://example.com/sync-file.pdf'
        }
      ]
      const option: FormChangeValueOption = {}
      
      fileUploadFactory.syncValue(mockFileUploadModel, newValue, option)

      expect(mockFileUploadModel.value).toEqual(newValue)
      // Ensure it's a new array instance
      expect(mockFileUploadModel.value).not.toBe(newValue)
    })

    it('should handle empty array', () => {
      const option: FormChangeValueOption = {}
      
      fileUploadFactory.syncValue(mockFileUploadModel, [], option)

      expect(mockFileUploadModel.value).toEqual([])
    })
  })
})
