// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { ImageFactory } from '../ImageFactory'
import {
  FormImageDefaultProps,
  FormImageSpecialKeys,
  FormElementImageModel
} from '@model/form/defines/FormImage'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption } from '@model/form/defines/shared'
import { AlignmentType } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => ({ ...obj })))

// Mock the makeTransactionResourceUrl function
jest.mock('@controller/contentLibrary/src/form', () => ({
  makeTransactionResourceUrl: jest.fn((boardId, transactionSequence, imageUUID, viewToken) => {
    return `https://example.com/board/${boardId}/transaction/${transactionSequence}/${imageUUID}${viewToken ? `?t=${viewToken}` : ''}`
  })
}))



describe('ImageFactory', () => {
  let imageFactory: ImageFactory
  let mockImageModel: FormElementImageModel
  let mockFormField: FormField

  beforeEach(() => {
    imageFactory = new ImageFactory()

    // Create a mock image model for testing
    mockImageModel = {
      ...FormImageDefaultProps,
      id: 'test-id',
      imageAlignment: AlignmentType.Center,
      imageAlt: 'Test image alt text',
      imageUUID: 'image-uuid-123',
      imageName: 'test-image.jpg',
      imageWidth: '300',
      supporting: 'Supporting text'
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'Image',
      fieldSpecific: {
        imageAlignment: AlignmentType.Center,
        imageAlt: 'Test image alt text',
        imageUUID: 'image-uuid-123',
        imageName: 'test-image.jpg',
        imageWidth: '300',
        supporting: 'Supporting text'
      }
    }
  })

  describe('create', () => {
    it('should create a new image model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = imageFactory.create(option)

      expect(result).toEqual({
        ...FormImageDefaultProps,
        type: 'Image',
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(FormImageDefaultProps)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = imageFactory.toServerData(mockImageModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockImageModel.id)
      expect(result.type).toEqual(mockImageModel.type)
      expect(result.fieldSpecific.imageAlignment).toEqual(mockImageModel.imageAlignment)
      expect(result.fieldSpecific.imageAlt).toEqual(mockImageModel.imageAlt)
      expect(result.fieldSpecific.imageUUID).toEqual(mockImageModel.imageUUID)
      expect(result.fieldSpecific.imageName).toEqual(mockImageModel.imageName)
      expect(result.fieldSpecific.imageWidth).toEqual(mockImageModel.imageWidth)
      expect(result.fieldSpecific.supporting).toEqual(mockImageModel.supporting)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {
        boardId: 'board-123',
        transactionSequence: 456,
        viewToken: 'view-token-789'
      }

      const result = imageFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.type).toEqual(mockFormField.type)
      expect(result.imageAlignment).toEqual(mockFormField.fieldSpecific.imageAlignment)
      expect(result.imageAlt).toEqual(mockFormField.fieldSpecific.imageAlt)
      expect(result.imageUUID).toEqual(mockFormField.fieldSpecific.imageUUID)
      expect(result.imageName).toEqual(mockFormField.fieldSpecific.imageName)
      expect(result.imageWidth).toEqual(mockFormField.fieldSpecific.imageWidth)
      expect(result.supporting).toEqual(mockFormField.fieldSpecific.supporting)
    })

    it('should set imageURL when imageUUID exists', () => {
      const option: FormElementTransformOption = {
        boardId: 'board-123',
        transactionSequence: 456,
        viewToken: 'view-token-789'
      }

      const result = imageFactory.toViewModel(mockFormField, option)

      expect(result.imageURL).toEqual('https://example.com/board/board-123/transaction/456/image-uuid-123?t=view-token-789')
    })

  })

  describe('validate', () => {
    it('should always validate successfully', async () => {
      const validationPromise = imageFactory.validate(mockImageModel)

      await expect(validationPromise).resolves.toBe(true)
    })
  })

  describe('resetValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockImageModel }

      imageFactory.resetValue(mockImageModel)

      // Verify that the model hasn't changed
      expect(mockImageModel).toEqual(modelCopy)
    })
  })

  describe('setValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockImageModel }
      const option: FormChangeValueOption = {}

      imageFactory.setValue(mockImageModel, 'new value', option)

      // Verify that the model hasn't changed
      expect(mockImageModel).toEqual(modelCopy)
    })
  })

  describe('syncValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockImageModel }
      const option: FormChangeValueOption = {}

      imageFactory.syncValue(mockImageModel, 'new value', option)

      // Verify that the model hasn't changed
      expect(mockImageModel).toEqual(modelCopy)
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = imageFactory.uiOption()

      expect(result).toEqual({
        view: 'FormImageView',
        editor: 'FormImageOption'
      })
    })
  })
})
