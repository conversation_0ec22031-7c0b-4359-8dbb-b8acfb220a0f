// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { HeadingFactory } from '../HeadingFactory'
import {
  HeadingDefaultProps,
  HeadingSpecialKeys,
  FormElementHeadingModel
} from '@model/form/defines/FormHeading'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption } from '@model/form/defines/shared'
import { AlignmentType } from '@model/form/defines/shared'
import uuid from 'uuid/v4'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock the processLangResource function
jest.mock('../shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
  })
}))

// Mock the makeTransactionResourceUrl function
jest.mock('@controller/contentLibrary/src/form', () => ({
  makeTransactionResourceUrl: jest.fn((boardId, transactionSequence, imageUUID, viewToken) => {
    return `https://example.com/board/${boardId}/transaction/${transactionSequence}/${imageUUID}${viewToken ? `?t=${viewToken}` : ''}`
  })
}))

describe('HeadingFactory', () => {
  let headingFactory: HeadingFactory
  let mockHeadingModel: FormElementHeadingModel
  let mockFormField: FormField

  beforeEach(() => {
    headingFactory = new HeadingFactory()

    // Create a mock heading model for testing
    mockHeadingModel = {
      ...HeadingDefaultProps,
      id: 'test-id',
      label: 'Test Heading',
      labelAlignment: AlignmentType.Center,
      imageAlignment: AlignmentType.Right,
      supporting: 'Supporting text',
      enableImage: true,
      imageUUID: 'image-uuid-123',
      imageName: 'test-image.jpg',
      imageWidth: '300',
      imageAlt: 'Test image alt text'
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'Heading',
      label: 'Test Heading',
      fieldSpecific: {
        labelAlignment: AlignmentType.Center,
        imageAlignment: AlignmentType.Right,
        supporting: 'Supporting text',
        enableImage: true,
        imageUUID: 'image-uuid-123',
        imageName: 'test-image.jpg',
        imageWidth: '300',
        imageAlt: 'Test image alt text'
      }
    }
  })

  describe('create', () => {
    it('should create a new heading model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = headingFactory.create(option)

      expect(result).toEqual({
        ...HeadingDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }

      // Set a label with $t placeholder to trigger translation
      const mockModel = headingFactory.create(option)
      mockModel.label = '$t(form.heading.label)'

      // Call processLangResource directly since it's called in the factory
      require('../shared').processLangResource(mockModel, option.$t)

      // Check that the label has been translated
      expect(mockModel.label).toEqual('translated-label')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = headingFactory.toServerData(mockHeadingModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockHeadingModel.id)
      expect(result.label).toEqual(mockHeadingModel.label)
      expect(result.fieldSpecific.labelAlignment).toEqual(mockHeadingModel.labelAlignment)
      expect(result.fieldSpecific.imageAlignment).toEqual(mockHeadingModel.imageAlignment)
      expect(result.fieldSpecific.supporting).toEqual(mockHeadingModel.supporting)
      expect(result.fieldSpecific.enableImage).toEqual(mockHeadingModel.enableImage)
      expect(result.fieldSpecific.imageUUID).toEqual(mockHeadingModel.imageUUID)
      expect(result.fieldSpecific.imageName).toEqual(mockHeadingModel.imageName)
      expect(result.fieldSpecific.imageWidth).toEqual(mockHeadingModel.imageWidth)
      expect(result.fieldSpecific.imageAlt).toEqual(mockHeadingModel.imageAlt)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {
        boardId: 'board-123',
        transactionSequence: 456,
        viewToken: 'view-token-789'
      }

      const result = headingFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.label).toEqual(mockFormField.label)
      expect(result.labelAlignment).toEqual(mockFormField.fieldSpecific.labelAlignment)
      expect(result.imageAlignment).toEqual(mockFormField.fieldSpecific.imageAlignment)
      expect(result.supporting).toEqual(mockFormField.fieldSpecific.supporting)
      expect(result.enableImage).toEqual(mockFormField.fieldSpecific.enableImage)
      expect(result.imageUUID).toEqual(mockFormField.fieldSpecific.imageUUID)
      expect(result.imageName).toEqual(mockFormField.fieldSpecific.imageName)
      expect(result.imageWidth).toEqual(mockFormField.fieldSpecific.imageWidth)
      expect(result.imageAlt).toEqual(mockFormField.fieldSpecific.imageAlt)
    })

    it('should set imageURL when enableImage is true and imageUUID exists', () => {
      const option: FormElementTransformOption = {
        boardId: 'board-123',
        transactionSequence: 456,
        viewToken: 'view-token-789'
      }

      const result = headingFactory.toViewModel(mockFormField, option)

      expect(result.imageURL).toEqual('https://example.com/board/board-123/transaction/456/image-uuid-123?t=view-token-789')
    })

    it('should not set imageURL when enableImage is false', () => {
      mockFormField.fieldSpecific.enableImage = false

      const option: FormElementTransformOption = {
        boardId: 'board-123',
        transactionSequence: 456,
        viewToken: 'view-token-789'
      }

      const result = headingFactory.toViewModel(mockFormField, option)

      expect(result.imageURL).toBeUndefined()
    })

    it('should not set imageURL when imageUUID is missing', () => {
      mockFormField.fieldSpecific.imageUUID = ''

      const option: FormElementTransformOption = {
        boardId: 'board-123',
        transactionSequence: 456,
        viewToken: 'view-token-789'
      }

      const result = headingFactory.toViewModel(mockFormField, option)

      expect(result.imageURL).toBeUndefined()
    })
  })

  describe('validate', () => {
    it('should always validate successfully', async () => {
      const validationPromise = headingFactory.validate(mockHeadingModel)

      await expect(validationPromise).resolves.toBe(true)
    })
  })

  describe('resetValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockHeadingModel }

      headingFactory.resetValue(mockHeadingModel)

      // Verify that the model hasn't changed
      expect(mockHeadingModel).toEqual(modelCopy)
    })
  })

  describe('setValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockHeadingModel }
      const option: FormChangeValueOption = {}

      headingFactory.setValue(mockHeadingModel, 'new value', option)

      // Verify that the model hasn't changed
      expect(mockHeadingModel).toEqual(modelCopy)
    })
  })

  describe('syncValue', () => {
    it('should do nothing when called', () => {
      const modelCopy = { ...mockHeadingModel }
      const option: FormChangeValueOption = {}

      headingFactory.syncValue(mockHeadingModel, 'new value', option)

      // Verify that the model hasn't changed
      expect(mockHeadingModel).toEqual(modelCopy)
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = headingFactory.uiOption()

      expect(result).toEqual({
        view: 'FormHeadingView',
        editor: 'FormHeadingOption'
      })
    })
  })
})
