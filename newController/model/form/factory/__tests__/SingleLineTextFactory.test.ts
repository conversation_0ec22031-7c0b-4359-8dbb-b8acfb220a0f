import { SingleLineTextFactory } from '../SingleLineTextFactory'
import {
  SingleLineTextDefaultProps,
  SingleLineTextSpecialKeys,
  FormElementSingleLineTextModel
} from '@model/form/defines/FormSingleLineText'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => {
  if (Array.isArray(obj)) {
    return [...obj]
  } else if (typeof obj === 'object' && obj !== null) {
    return { ...obj }
  }
  return obj
}))

// Mock the processLangResource function
jest.mock('@model/form/factory/shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  }),
  removeDDRSource: jest.fn((value) => value)
}))

// Mock syncFormElementSampleValue
jest.mock('@model/form/factory/utils', () => ({
  syncFormElementSampleValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = value
    }
  })
}))

// Mock getFieldSpecificFormViewModel
jest.mock('@model/form/transform/common', () => ({
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      if (model[key] !== undefined) {
        result[key] = model[key]
      }
    })
    return result
  })
}))

describe('SingleLineTextFactory', () => {
  let singleLineTextFactory: SingleLineTextFactory
  let mockFormField: FormField
  let mockSingleLineTextModel: FormElementSingleLineTextModel

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Initialize the factory
    singleLineTextFactory = new SingleLineTextFactory()

    // Mock FormField data
    mockFormField = {
      id: 'test-id',
      type: 'SingleLineText',
      uniqueName: 'test-unique-name',
      value: 'Test value',
      name: 'Test name',
      label: 'Test label',
      defaultValue: 'Default value',
      fieldSpecific: {
        hideLabel: false,
        placeholder: 'Test placeholder',
        readonly: false,
        required: true,
        supporting: 'Test supporting text',
        isProtected: false,
        minLength: '5',
        maxLength: '100'
      }
    } as FormField

    // Mock SingleLineTextModel
    mockSingleLineTextModel = {
      ...SingleLineTextDefaultProps,
      id: 'test-id',
      uniqueName: 'test-unique-name',
      value: 'Test value',
      name: 'Test name',
      label: 'Test label',
      defaultValue: 'Default value',
      hideLabel: false,
      placeholder: 'Test placeholder',
      readonly: false,
      required: true,
      supporting: 'Test supporting text',
      isProtected: false,
      minLength: '5',
      maxLength: '100',
      errors: []
    }
  })

  describe('create', () => {
    it('should create a new single line text model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = singleLineTextFactory.create(option)

      expect(result).toEqual({
        ...SingleLineTextDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(SingleLineTextDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = singleLineTextFactory.create(option)

      expect(result.label).toEqual('translated-label')
      expect(result.placeholder).toEqual('translated-placeholder')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = singleLineTextFactory.toServerData(mockSingleLineTextModel)

      expect(result).toEqual({
        id: 'test-id',
        type: 'SingleLineText',
        uniqueName: 'test-unique-name',
        value: 'Test value',
        name: 'Test name',
        label: 'Test label',
        defaultValue: 'Default value',
        fieldSpecific: expect.any(Object)
      })
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = singleLineTextFactory.toViewModel(mockFormField)

      expect(result).toEqual({
        ...SingleLineTextDefaultProps,
        id: 'test-id',
        uniqueName: 'test-unique-name',
        value: 'Test value',
        label: 'Test label',
        name: 'Test name',
        defaultValue: 'Default value',
        hideLabel: false,
        placeholder: 'Test placeholder',
        readonly: false,
        required: true,
        supporting: 'Test supporting text',
        isProtected: false,
        minLength: '5',
        maxLength: '100'
      })
    })

    it('should apply default value when option.applyDefaultValue is true', () => {
      const option: FormElementTransformOption = { applyDefaultValue: true }
      const result = singleLineTextFactory.toViewModel(mockFormField, option)

      expect(result.value).toEqual('Default value')
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      const validationPromise = singleLineTextFactory.validate(mockSingleLineTextModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSingleLineTextModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockSingleLineTextModel.value = ''

      const validationPromise = singleLineTextFactory.validate(mockSingleLineTextModel)

      await expect(validationPromise).rejects.toEqual(mockSingleLineTextModel)
      expect(mockSingleLineTextModel.errors).toHaveLength(1)
      expect(mockSingleLineTextModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })

    it('should not validate when value exceeds maxLength', async () => {
      mockSingleLineTextModel.value = 'a'.repeat(101) // Exceeds maxLength of 100
      mockSingleLineTextModel.required = false

      const validationPromise = singleLineTextFactory.validate(mockSingleLineTextModel)

      await expect(validationPromise).rejects.toEqual(mockSingleLineTextModel)
      expect(mockSingleLineTextModel.errors).toHaveLength(1)
      expect(mockSingleLineTextModel.errors[0].errorType).toEqual(FormErrorType.AnswerMaxLimit)
      expect(mockSingleLineTextModel.errors[0].params.maxLength).toEqual('100')
    })

    it('should not validate when value is below minLength', async () => {
      mockSingleLineTextModel.value = 'abc' // Below minLength of 5
      mockSingleLineTextModel.required = false

      const validationPromise = singleLineTextFactory.validate(mockSingleLineTextModel)

      await expect(validationPromise).rejects.toEqual(mockSingleLineTextModel)
      expect(mockSingleLineTextModel.errors).toHaveLength(1)
      expect(mockSingleLineTextModel.errors[0].errorType).toEqual(FormErrorType.AnswerMinLimit)
      expect(mockSingleLineTextModel.errors[0].params.minLength).toEqual('5')
    })

    it('should validate template with defaultValue when isValidateTemplate is true', async () => {
      const option: FormValidateOptions = { isValidateTemplate: true }
      mockSingleLineTextModel.value = ''
      mockSingleLineTextModel.defaultValue = 'Valid default'

      const validationPromise = singleLineTextFactory.validate(mockSingleLineTextModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSingleLineTextModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty string', () => {
      singleLineTextFactory.resetValue(mockSingleLineTextModel)

      expect(mockSingleLineTextModel.value).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for regular form', () => {
      const runtimeOption = { isPDFForm: false }
      const result = singleLineTextFactory.uiOption(runtimeOption)

      expect(result).toEqual({
        view: 'FormSingleLineTextView',
        editor: 'FormSingleLineText'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const runtimeOption = { isPDFForm: true }
      const result = singleLineTextFactory.uiOption(runtimeOption)

      expect(result).toEqual({
        view: 'PDFFormSingleLineTextView',
        editor: 'FormInputTextSetting'
      })
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {}
      
      singleLineTextFactory.setValue(mockSingleLineTextModel, 'New value', option)

      expect(mockSingleLineTextModel.value).toEqual('New value')
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const option = {
        focusedElementId: 'other-id'
      }
      
      singleLineTextFactory.syncValue(mockSingleLineTextModel, 'New value', option)

      expect(mockSingleLineTextModel.value).toEqual('New value')
    })

    it('should not sync the value when focused', () => {
      const option = {
        focusedElementId: 'test-id'
      }
      
      singleLineTextFactory.syncValue(mockSingleLineTextModel, 'New value', option)

      expect(mockSingleLineTextModel.value).toEqual('Test value')
    })
  })
})
