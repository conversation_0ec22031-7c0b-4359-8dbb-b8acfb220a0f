import { UserNameFactory } from '../UserNameFactory'
import {
  UserNameDefaultProps,
  UserNameDefaultValue,
  UserNameSpecialKeys,
  FormElementUserNameModel,
  UserNameValue
} from '@model/form/defines/FormUserName'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => {
  if (Array.isArray(obj)) {
    return [...obj]
  } else if (typeof obj === 'object' && obj !== null) {
    return { ...obj }
  }
  return obj
}))

// Mock the processLangResource function
jest.mock('@model/form/factory/shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

// Mock form element utils
jest.mock('@model/form/factory/utils', () => ({
  setFormElementValue: jest.fn((model, value, option) => {
    model.value = value
  }),
  syncFormElementObjectValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = { ...model.value, ...value }
    }
  })
}))

// Mock getFieldSpecificFormViewModel
jest.mock('@model/form/transform/common', () => ({
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      if (model[key] !== undefined) {
        result[key] = model[key]
      }
    })
    return result
  })
}))

describe('UserNameFactory', () => {
  let userNameFactory: UserNameFactory
  let mockFormField: FormField
  let mockUserNameModel: FormElementUserNameModel
  let mockUserNameValue: UserNameValue

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Initialize the factory
    userNameFactory = new UserNameFactory()

    // Mock UserNameValue
    mockUserNameValue = {
      firstName: 'John',
      lastName: 'Doe',
      prefix: 'Mr.',
      middleName: 'William',
      suffix: 'Jr.'
    }

    // Mock FormField data
    mockFormField = {
      id: 'test-id',
      type: 'UserName',
      value: mockUserNameValue,
      label: 'Test label',
      defaultValue: {
        firstName: 'Default First',
        lastName: 'Default Last',
        prefix: '',
        middleName: '',
        suffix: ''
      },
      fieldSpecific: {
        showMiddleName: true,
        showPrefix: true,
        showSuffix: true,
        defaultFromProfile: false,
        supporting: 'Test supporting text',
        required: true,
        hideLabel: false,
        isProtected: false,
        readonly: false,
        prefixOptions: ['Mr.', 'Mrs.', 'Ms.']
      }
    } as FormField

    // Mock UserNameModel
    mockUserNameModel = {
      ...UserNameDefaultProps,
      id: 'test-id',
      value: mockUserNameValue,
      label: 'Test label',
      defaultValue: {
        firstName: 'Default First',
        lastName: 'Default Last',
        prefix: '',
        middleName: '',
        suffix: ''
      },
      showMiddleName: true,
      showPrefix: true,
      showSuffix: true,
      defaultFromProfile: false,
      supporting: 'Test supporting text',
      required: true,
      hideLabel: false,
      isProtected: false,
      readonly: false,
      prefixOptions: ['Mr.', 'Mrs.', 'Ms.'],
      errors: []
    }
  })

  describe('create', () => {
    it('should create a new user name model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = userNameFactory.create(option)

      expect(result).toEqual({
        ...UserNameDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(UserNameDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = userNameFactory.create(option)

      expect(require('@model/form/factory/shared').processLangResource).toHaveBeenCalledWith(expect.anything(), option.$t)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = userNameFactory.toServerData(mockUserNameModel)

      expect(result).toEqual({
        id: 'test-id',
        type: 'UserName',
        value: mockUserNameValue,
        label: 'Test label',
        fieldSpecific: expect.any(Object)
      })
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = userNameFactory.toViewModel(mockFormField)

      expect(result).toEqual({
        ...UserNameDefaultProps,
        id: 'test-id',
        value: mockUserNameValue,
        label: 'Test label',
        showMiddleName: true,
        showPrefix: true,
        showSuffix: true,
        defaultFromProfile: false,
        supporting: 'Test supporting text',
        required: true,
        hideLabel: false,
        isProtected: false,
        readonly: false,
        prefixOptions: ['Mr.', 'Mrs.', 'Ms.']
      })
    })

    it('should apply default value when option.applyDefaultValue is true', () => {
      const option: FormElementTransformOption = { 
        applyDefaultValue: true,
        boardId: 'test-board',
        transactionSequence: 1
      }
      const result = userNameFactory.toViewModel(mockFormField, option)

      expect(result.value).toEqual({
        firstName: 'Default First',
        lastName: 'Default Last',
        prefix: '',
        middleName: '',
        suffix: ''
      })
    })

    it('should merge default value with existing value when applyDefaultValue is false', () => {
      const fieldWithPartialValue = {
        ...mockFormField,
        value: { firstName: 'John' },
        defaultValue: {
          firstName: 'Default First',
          lastName: 'Default Last',
          prefix: 'Mr.',
          middleName: '',
          suffix: ''
        }
      }
      
      const result = userNameFactory.toViewModel(fieldWithPartialValue)

      expect(result.value).toEqual({
        firstName: 'John',
        lastName: 'Default Last',
        prefix: 'Mr.',
        middleName: '',
        suffix: ''
      })
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      const validationPromise = userNameFactory.validate(mockUserNameModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockUserNameModel.errors).toEqual([])
    })

    it('should not validate when required firstName is empty', async () => {
      mockUserNameModel.value.firstName = ''

      const validationPromise = userNameFactory.validate(mockUserNameModel)

      await expect(validationPromise).rejects.toEqual(mockUserNameModel)
      expect(mockUserNameModel.errors).toHaveLength(1)
      expect(mockUserNameModel.errors[0].errorType).toEqual(FormErrorType.Required)
      expect(mockUserNameModel.errors[0].field).toEqual('firstName')
    })

    it('should not validate when required lastName is empty', async () => {
      mockUserNameModel.value.lastName = ''

      const validationPromise = userNameFactory.validate(mockUserNameModel)

      await expect(validationPromise).rejects.toEqual(mockUserNameModel)
      expect(mockUserNameModel.errors).toHaveLength(1)
      expect(mockUserNameModel.errors[0].errorType).toEqual(FormErrorType.Required)
      expect(mockUserNameModel.errors[0].field).toEqual('lastName')
    })

    it('should validate successfully when not required and fields are empty', async () => {
      mockUserNameModel.required = false
      mockUserNameModel.value.firstName = ''
      mockUserNameModel.value.lastName = ''

      const validationPromise = userNameFactory.validate(mockUserNameModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockUserNameModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to default', () => {
      userNameFactory.resetValue(mockUserNameModel)

      expect(mockUserNameModel.value).toEqual({
        ...UserNameDefaultValue
      })
      expect(cloneDeep).toHaveBeenCalledWith(UserNameDefaultValue)
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = userNameFactory.uiOption()

      expect(result).toEqual({
        view: 'FormUserNameView',
        editor: 'FormUserName'
      })
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {
        boardId: 'test-board',
        transactionSequence: 1
      }
      const newValue: UserNameValue = {
        firstName: 'Jane',
        lastName: 'Smith',
        prefix: 'Ms.',
        middleName: '',
        suffix: ''
      }
      
      userNameFactory.setValue(mockUserNameModel, newValue, option)

      expect(require('@model/form/factory/utils').setFormElementValue).toHaveBeenCalledWith(mockUserNameModel, newValue, option)
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const option = {
        focusedElementId: 'other-id',
        boardId: 'test-board',
        transactionSequence: 1
      }
      const newValue: UserNameValue = {
        firstName: 'Jane',
        lastName: 'Smith',
        prefix: 'Ms.',
        middleName: '',
        suffix: ''
      }
      
      userNameFactory.syncValue(mockUserNameModel, newValue, option)

      expect(require('@model/form/factory/utils').syncFormElementObjectValue).toHaveBeenCalledWith(mockUserNameModel, newValue, option)
    })
  })
})
