// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { NumberFactory } from '../NumberFactory'
import {
  FormElementNumberModel,
  NumberDefaultProps,
  NumberSpecialKeys
} from '@model/form/defines/FormNumber'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import { ConditionRuleViewModel, ConditionOperatorType } from '@model/form/defines/condition'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => ({ ...obj })))

// Mock the processLangResource and removeDDRSource functions
jest.mock('../shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  }),
  removeDDRSource: jest.fn(value => value),
  isDefine: jest.fn(value => value !== undefined)
}))

// Mock the isNumberValueMeetExpected function
jest.mock('@model/form/common/condition', () => ({
  isNumberValueMeetExpected: jest.fn((rule, value) => {
    if (!value) return false

    const expectedValue = parseInt(rule.expectedValue, 10)
    const numValue = parseInt(value, 10)

    switch(rule.operator) {
      case '=':
        return numValue === expectedValue
      case '<>':
        return numValue !== expectedValue
      case '>':
        return numValue > expectedValue
      case '<':
        return numValue < expectedValue
      default:
        return false
    }
  })
}))

// Mock the syncFormElementSampleValue function
jest.mock('@model/form/factory/utils', () => ({
  syncFormElementSampleValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = value
    }
  })
}))

// Mock the removeDDRSource function
jest.mock('../shared', () => ({
  removeDDRSource: jest.fn((value) => value),
  processLangResource: jest.fn(),
  isDefine: jest.fn((value) => value !== null && value !== undefined)
}))

// Mock the getAttrsFromViewModel and getAttrsFromFormField functions
jest.mock('@model/form/common/utils', () => ({
  getAttrsFromViewModel: jest.fn((model, defaultModel, specialKeys) => {
    // Simple mock implementation that returns a FormField
    return {
      id: model.id,
      type: model.type,
      value: model.value,
      defaultValue: model.defaultValue,
      fieldSpecific: {
        precision: model.precision,
        minLength: model.minLength,
        maxLength: model.maxLength,
        hideLabel: model.hideLabel,
        placeholder: model.placeholder,
        required: model.required,
        readonly: model.readonly,
        isProtected: model.isProtected,
        supporting: model.supporting
      }
    }
  }),
  getAttrsFromFormField: jest.fn((data, defaultModel, specialKeys, options) => {
    // Simple mock implementation that returns a FormElementNumberModel
    const model = {
      ...cloneDeep(defaultModel),
      id: data.id,
      type: data.type,
      value: data.value,
      defaultValue: data.defaultValue,
      precision: data.fieldSpecific.precision,
      minLength: data.fieldSpecific.minLength,
      maxLength: data.fieldSpecific.maxLength,
      hideLabel: data.fieldSpecific.hideLabel,
      placeholder: data.fieldSpecific.placeholder,
      required: data.fieldSpecific.required,
      readonly: data.fieldSpecific.readonly,
      isProtected: data.fieldSpecific.isProtected,
      supporting: data.fieldSpecific.supporting
    }

    // Apply transforms if provided
    if (options?.transforms?.value && data.defaultValue) {
      model.value = data.defaultValue
    }

    return model
  }),
  ModelAttrTransforms: jest.fn(),
  useDefaultValue: jest.fn((data) => data.defaultValue)
}))

describe('NumberFactory', () => {
  let numberFactory: NumberFactory
  let mockNumberModel: FormElementNumberModel
  let mockFormField: FormField

  beforeEach(() => {
    numberFactory = new NumberFactory()

    // Create a mock number model for testing
    mockNumberModel = {
      ...NumberDefaultProps,
      id: 'test-id',
      label: 'Test Number',
      value: '123',
      defaultValue: '456',
      precision: 0,
      minLength: '10',
      maxLength: '1000',
      required: true
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'Number',
      value: '123',
      defaultValue: '456',
      fieldSpecific: {
        precision: 0,
        minLength: '10',
        maxLength: '1000',
        hideLabel: false,
        placeholder: '123',
        required: true,
        readonly: false,
        isProtected: false,
        supporting: 'Supporting text'
      }
    }

    // Reset all mocks before each test
    jest.clearAllMocks()
  })

  describe('create', () => {
    it('should create a new number model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = numberFactory.create(option)

      expect(result).toEqual({
        ...NumberDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(NumberDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = numberFactory.create(option)

      expect(require('../shared').processLangResource).toHaveBeenCalledWith(expect.anything(), option.$t)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = numberFactory.toServerData(mockNumberModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockNumberModel.id)
      expect(result.type).toEqual(mockNumberModel.type)
      expect(result.value).toEqual(mockNumberModel.value)
      expect(result.defaultValue).toEqual(mockNumberModel.defaultValue)
      expect(result.fieldSpecific.precision).toEqual(mockNumberModel.precision)
      expect(result.fieldSpecific.minLength).toEqual(mockNumberModel.minLength)
      expect(result.fieldSpecific.maxLength).toEqual(mockNumberModel.maxLength)
      expect(result.fieldSpecific.required).toEqual(mockNumberModel.required)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {}
      const result = numberFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.type).toEqual(mockFormField.type)
      expect(result.value).toEqual(mockFormField.value)
      expect(result.defaultValue).toEqual(mockFormField.defaultValue)
      expect(result.precision).toEqual(mockFormField.fieldSpecific.precision)
      expect(result.minLength).toEqual(mockFormField.fieldSpecific.minLength)
      expect(result.maxLength).toEqual(mockFormField.fieldSpecific.maxLength)
      expect(result.required).toEqual(mockFormField.fieldSpecific.required)
      expect(result.placeholder).toEqual(mockFormField.fieldSpecific.placeholder)
    })

    it('should apply default value when option.applyDefaultValue is true', () => {
      const option: FormElementTransformOption = {
        applyDefaultValue: true
      }

      const result = numberFactory.toViewModel(mockFormField, option)

      expect(result.value).toEqual(mockFormField.defaultValue)
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      // Set value to meet min/max requirements
      mockNumberModel.value = '500'

      const validationPromise = numberFactory.validate(mockNumberModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockNumberModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockNumberModel.value = ''

      const validationPromise = numberFactory.validate(mockNumberModel)

      await expect(validationPromise).rejects.toEqual(mockNumberModel)
      expect(mockNumberModel.errors).toHaveLength(1)
      expect(mockNumberModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })

    it('should not validate when value is less than minLength', async () => {
      mockNumberModel.value = '5'
      mockNumberModel.minLength = '10'

      const validationPromise = numberFactory.validate(mockNumberModel)

      await expect(validationPromise).rejects.toEqual(mockNumberModel)
      expect(mockNumberModel.errors).toHaveLength(1)
      expect(mockNumberModel.errors[0].errorType).toEqual(FormErrorType.MinLimit)
      expect(mockNumberModel.errors[0].params.minLength).toEqual(10)
    })

    it('should not validate when value is greater than maxLength', async () => {
      mockNumberModel.value = '1500'
      mockNumberModel.maxLength = '1000'

      const validationPromise = numberFactory.validate(mockNumberModel)

      await expect(validationPromise).rejects.toEqual(mockNumberModel)
      expect(mockNumberModel.errors).toHaveLength(1)
      expect(mockNumberModel.errors[0].errorType).toEqual(FormErrorType.MaxLimit)
      expect(mockNumberModel.errors[0].params.maxLength).toEqual(1000)
    })

    it('should validate template even if required field is empty', async () => {
      mockNumberModel.value = ''
      const option: FormValidateOptions = {
        isValidateTemplate: true
      }

      const validationPromise = numberFactory.validate(mockNumberModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockNumberModel.errors).toEqual([])
    })

    it('should use removeDDRSource when enableDDR is true', async () => {
      mockNumberModel.value = '${some-ddr-value}'
      const option: FormValidateOptions = {
        enableDDR: true
      }

      await numberFactory.validate(mockNumberModel, option)

      expect(require('../shared').removeDDRSource).toHaveBeenCalledWith(mockNumberModel.value)
    })
  })

  describe('resetValue', () => {
    it('should reset the value to null', () => {
      mockNumberModel.value = '123'

      numberFactory.resetValue(mockNumberModel)

      expect(mockNumberModel.value).toBeNull()
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = numberFactory.uiOption()

      expect(result).toEqual({
        view: 'FormNumberView',
        editor: 'FormNumber'
      })
    })
  })

  describe('meetExpected', () => {
    it('should check if the value meets the expected condition (Equal)', () => {
      mockNumberModel.value = '100'
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.Equal,
        expectedValue: '100'
      }

      const result = numberFactory.meetExpected(mockNumberModel, rule)

      expect(result).toBe(true)
      expect(require('@model/form/common/condition').isNumberValueMeetExpected).toHaveBeenCalledWith(rule, mockNumberModel.value)
    })

    it('should check if the value meets the expected condition (NotEqual)', () => {
      mockNumberModel.value = '100'
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.NotEqual,
        expectedValue: '200'
      }

      const result = numberFactory.meetExpected(mockNumberModel, rule)

      expect(result).toBe(true)
      expect(require('@model/form/common/condition').isNumberValueMeetExpected).toHaveBeenCalledWith(rule, mockNumberModel.value)
    })

    it('should check if the value meets the expected condition (GreaterThan)', () => {
      mockNumberModel.value = '300'
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.GreaterThan,
        expectedValue: '200'
      }

      const result = numberFactory.meetExpected(mockNumberModel, rule)

      expect(result).toBe(true)
      expect(require('@model/form/common/condition').isNumberValueMeetExpected).toHaveBeenCalledWith(rule, mockNumberModel.value)
    })

    it('should check if the value meets the expected condition (LessThan)', () => {
      mockNumberModel.value = '100'
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.LessThan,
        expectedValue: '200'
      }

      const result = numberFactory.meetExpected(mockNumberModel, rule)

      expect(result).toBe(true)
      expect(require('@model/form/common/condition').isNumberValueMeetExpected).toHaveBeenCalledWith(rule, mockNumberModel.value)
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {}
      
      numberFactory.setValue(mockNumberModel, '999', option)

      expect(mockNumberModel.value).toEqual('999')
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const option = {
        focusedElementId: 'other-id'
      }
      
      numberFactory.syncValue(mockNumberModel, 999, option)

      expect(mockNumberModel.value).toEqual(999)
      expect(require('@model/form/factory/utils').syncFormElementSampleValue).toHaveBeenCalledWith(mockNumberModel, 999, option)
    })

    it('should not sync the value when focused', () => {
      const option = {
        focusedElementId: 'test-id'
      }
      
      numberFactory.syncValue(mockNumberModel, 999, option)

      expect(mockNumberModel.value).toEqual('123')
      expect(require('@model/form/factory/utils').syncFormElementSampleValue).toHaveBeenCalledWith(mockNumberModel, 999, option)
    })
  })
})
