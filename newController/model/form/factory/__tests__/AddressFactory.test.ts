import {AddressFactory} from '../AddressFactory'
import {
    AddressDefaultProps,
    AddressDefaultValue,
    AddressValue,
    FormElementAddressModel
} from '@model/form/defines/FormAddress'
import {FormField} from '@model/form/defines/serverDataStructure'
import {FormErrorType} from '@model/form/defines/base'

import {FormChangeValueOption, FormElementCreateOption, FormElementTransformOption} from '../defines/shared'
import uuid from 'uuid/v4'
// Mock uuid to return a predictable value for testing
// jest.mock('../shared',() =>({
//   processLangResource:jest.fn((key, $t)=>{
//     return $t(key)
//   })
// }))
// Mock the MxISDK module
jest.mock('isdk', () => ({
    MxISDK: {
        getCurrentUser: jest.fn(() => {
            basicInfo: {
                timezone: 'America/Los_Angeles'
            }
        })
    }
}))

// Mock moment-timezone
jest.mock('moment-timezone', () => ({
    tz: {
        guess: jest.fn(() => 'America/Los_Angeles')
    }
}))
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

describe('AddressFactory', () => {
    let addressFactory: AddressFactory
    let mockFormField: FormField
    let mockAddressModel: FormElementAddressModel
    let mockAddressValue: AddressValue

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks()

        // Initialize the factory
        addressFactory = new AddressFactory()

        // Create mock data for tests
        mockAddressValue = {
            addressLineOne: '123 Test St',
            addressLineTwo: 'Apt 456',
            city: 'Test City',
            state: 'TS',
            zipcode: '12345',
            countryCode: 'US',
            countryDisplayName: 'United States'
        }

        mockAddressModel = {
            ...AddressDefaultProps,
            id: 'test-id',
            value: {...mockAddressValue},
            showAddressLineTwo: true,
            showCity: true,
            showState: true,
            showZipcode: true,
            showCountry: true,
            required: true,
            lineOneLabel: 'label-one',
            lineTwoLabel: 'label-two',
            cityLabel: 'label-city',
            stateLabel: 'label-state',
            zipCodeLabel: 'label-zip',
            countryLabel: 'label-country'
        }

        mockFormField = {
            id: 'test-id',
            name: 'test-address',
            type: 'Address',
            value: {...mockAddressValue},
            defaultValue: {...AddressDefaultValue},
            lineOneLabel: 'label-one',
            lineTwoLabel: 'label-two',
            cityLabel: 'label-city',
            stateLabel: 'label-state',
            zipCodeLabel: 'label-zip',
            countryLabel: 'label-country',
            fieldSpecific:{
                lockCountry:true,
                showAddressLineTwo: true,
                showCity: true,
                showState: true,
                showZipcode: true,
                showCountry: true,
                hideLabel: false,
                required: false,
            }
        }
    })

    describe('create', () => {
      it('should create a new address model with default properties', () => {
        const option: FormElementCreateOption = {}
        const result = addressFactory.create(option)

        expect(result).toEqual({
          ...AddressDefaultProps,
          id: 'mocked-uuid'
        })
        expect(uuid).toHaveBeenCalledTimes(1)
      })

      it('should process language resources if $t is provided', () => {
        const option: FormElementCreateOption = {
          $t: ($key: string) => `translated-${$key}`
        }
        const result = addressFactory.create(option)

        // Since processLangResource is mocked, we can't test the exact behavior
        // but we can verify that the id is set correctly
        expect(result.id).toEqual('mocked-uuid')
      })
    })

    describe('toServerData', () => {
        it('should convert view model to server data', () => {
            const result = addressFactory.toServerData(mockAddressModel)

            // Expect the result to contain all the properties from the model
            expect(result).toBeDefined()
            expect(result.id).toEqual(mockAddressModel.id)
            expect(result.value).toEqual(mockAddressModel.value)

            // Check that result.fieldSpecific has the required properties
            expect(result.fieldSpecific).toBeDefined()
            const requiredProps = [
                'lockCountry',
                'showAddressLineTwo',
                'showCity',
                'showState',
                'showZipcode',
                'showCountry',
                'hideLabel',
                'required'
            ]
            requiredProps.forEach(prop => {
                expect(result.fieldSpecific).toHaveProperty(prop)
            });
        })
    })

    describe('toViewModel', () => {
      it('should convert server data to view model', () => {
        const result = addressFactory.toViewModel(mockFormField)

        expect(result).toBeDefined()
        expect(result.id).toEqual(mockFormField.id)
        expect(result.value).toEqual(mockFormField.value)
      })

      it('should apply default value when option.applyDefaultValue is true and defaultValue exists', () => {
        const option: FormElementTransformOption = {
          applyDefaultValue: true
        }

        const result = addressFactory.toViewModel(mockFormField, option)

        expect(result).toBeDefined()
        // In a real scenario, the useDefaultValue function would be called
        // and the value would be set to the defaultValue
      })
    })

    describe('validate', () => {
      it('should validate successfully when all required fields are filled', async () => {
        const validationPromise = addressFactory.validate(mockAddressModel)

        await expect(validationPromise).resolves.toBe(true)
        expect(mockAddressModel.errors).toEqual([])
      })

      it('should fail validation when required fields are missing', async () => {
        // Create a model with empty required fields
        const invalidModel: FormElementAddressModel = {
          ...mockAddressModel,
          showAddressLineTwo: false, // Don't validate addressLineTwo
          value: {
            ...AddressDefaultValue,
            // Override countryCode to be empty for testing
            countryCode: ''
          }
        }

        try {
          await addressFactory.validate(invalidModel)
          fail('Validation should have failed')
        } catch (error) {
          expect(error).toBe(invalidModel)
          expect(invalidModel.errors).toContainEqual({
            field: 'addressLineOne',
            errorType: FormErrorType.Required
          })
          expect(invalidModel.errors).not.toContainEqual({
            field: 'addressLineTwo',
            errorType: FormErrorType.Required
          })
          expect(invalidModel.errors).toContainEqual({
            field: 'city',
            errorType: FormErrorType.Required
          })
          expect(invalidModel.errors).toContainEqual({
            field: 'state',
            errorType: FormErrorType.Required
          })
          expect(invalidModel.errors).toContainEqual({
            field: 'zipcode',
            errorType: FormErrorType.Required
          })
          expect(invalidModel.errors).toContainEqual({
            field: 'countryCode',
            errorType: FormErrorType.Required
          })
        }
      })

      it('should not validate fields that are not shown', async () => {
        // Create a model with some fields not shown
        const partialModel: FormElementAddressModel = {
          ...mockAddressModel,
          showAddressLineTwo: false,
          showCity: false,
          showState: false,
          showZipcode: false,
          showCountry: false,
          value: {
            ...AddressDefaultValue,
            addressLineOne: '123 Test St' // Only addressLineOne is filled
          }
        }

        await expect(addressFactory.validate(partialModel)).resolves.toBe(true)
        expect(partialModel.errors).toEqual([])
      })

      it('should not validate when required is false', async () => {
        // Create a model with required set to false
        const nonRequiredModel: FormElementAddressModel = {
          ...mockAddressModel,
          required: false,
          value: {
            ...AddressDefaultValue // All fields are empty
          }
        }

        await expect(addressFactory.validate(nonRequiredModel)).resolves.toBe(true)
        expect(nonRequiredModel.errors).toEqual([])
      })



      // Test to verify validation matches JSON schema conditional logic
      it('should validate according to JSON schema conditional rules', async () => {
        // Test each condition separately

        // 1. Test with only addressLineOne required (base case)
        const baseModel: FormElementAddressModel = {
          ...mockAddressModel,
          showAddressLineTwo: false,
          showCity: false,
          showState: false,
          showZipcode: false,
          showCountry: false,
          value: {
            ...AddressDefaultValue,
            addressLineOne: '123 Test St' // Only addressLineOne is filled
          }
        }
        await expect(addressFactory.validate(baseModel)).resolves.toBe(true)

        // 2. Test with city shown but empty
        const cityModel: FormElementAddressModel = {
          ...baseModel,
          showCity: true,
          value: {
            ...baseModel.value,
            city: '' // Empty city
          }
        }
        try {
          await addressFactory.validate(cityModel)
          fail('Validation should have failed due to empty city')
        } catch (error) {
          expect(error).toBe(cityModel)
          expect(cityModel.errors).toContainEqual({
            field: 'city',
            errorType: FormErrorType.Required
          })
        }

        // 3. Test with country shown but empty
        const countryModel: FormElementAddressModel = {
          ...baseModel,
          showCountry: true,
          value: {
            ...baseModel.value,
            countryCode: '' // Empty country
          }
        }
        try {
          await addressFactory.validate(countryModel)
          fail('Validation should have failed due to empty country')
        } catch (error) {
          expect(error).toBe(countryModel)
          expect(countryModel.errors).toContainEqual({
            field: 'countryCode',
            errorType: FormErrorType.Required
          })
        }
      })
    })
    //
    describe('resetValue', () => {
      it('should reset the value to default', () => {
        addressFactory.resetValue(mockAddressModel)

        expect(mockAddressModel.value).toEqual(AddressDefaultValue)
      })
    })

    describe('uiOption', () => {
      it('should return the correct UI options', () => {
        const result = addressFactory.uiOption()

        expect(result).toEqual({
          view: 'FormAddressView',
          editor: 'FormAddressOption'
        })
      })
    })
    //
    describe('setValue', () => {
      it('should set the value correctly', () => {
        const newValue: AddressValue = {
          addressLineOne: 'New Address',
          addressLineTwo: 'New Apt',
          city: 'New City',
          state: 'NS',
          zipcode: '54321',
          countryCode: 'CA',
          countryDisplayName: 'Canada'
        }

        const option: FormChangeValueOption = {}

        addressFactory.setValue(mockAddressModel, newValue, option)

        expect(mockAddressModel.value).toEqual(newValue)
      })

      it('should set only the changed property when changedProperty is specified', () => {
        const originalValue = { ...mockAddressModel.value }
        const newValue: AddressValue = {
          ...originalValue,
          city: 'Changed City'
        }

        const option: FormChangeValueOption = {
          changedProperty: 'city'
        }

        addressFactory.setValue(mockAddressModel, newValue, option)

        expect(mockAddressModel.value.city).toEqual('Changed City')
        // Other properties should remain unchanged
        expect(mockAddressModel.value.addressLineOne).toEqual(originalValue.addressLineOne)
      })
    })

    describe('syncValue', () => {
      it('should sync values correctly', () => {
        const newValue: AddressValue = {
          addressLineOne: 'Synced Address',
          addressLineTwo: 'Synced Apt',
          city: 'Synced City',
          state: 'SS',
          zipcode: '12345',
          countryCode: 'UK',
          countryDisplayName: 'United Kingdom'
        }

        const option: FormChangeValueOption = {
          focusedElementId: 'other-id', // Different from the model's id
          focusedProperty: undefined
        }

        addressFactory.syncValue(mockAddressModel, newValue, option)

        // When the focused element is different, all values should be synced
        expect(mockAddressModel.value).toEqual({
          ...mockAddressModel.value,
          ...newValue
        })
      })

      it('should not sync the focused property when the model is the focused element', () => {
        const originalValue = { ...mockAddressModel.value }
        const newValue: AddressValue = {
          ...originalValue,
          city: 'Focused City'
        }

        const option: FormChangeValueOption = {
          focusedElementId: 'test-id', // Same as the model's id
          focusedProperty: 'city'
        }

        addressFactory.syncValue(mockAddressModel, newValue, option)

        // The focused property should not be synced
        expect(mockAddressModel.value.city).toEqual(originalValue.city)
        // Other properties should be synced
        expect(mockAddressModel.value).toEqual(originalValue)
      })
    })
})
