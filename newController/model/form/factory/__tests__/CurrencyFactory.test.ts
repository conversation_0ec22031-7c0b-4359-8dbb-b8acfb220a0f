// Import ConditionOperatorType first for the mock
import { ConditionOperatorType } from '@model/form/defines/condition'

// Mock problematic modules first
jest.mock('@model/form/common/condition', () => ({
  isNumberValueMeetExpected: jest.fn((rule, value) => {
    // Mock the actual logic for testing
    const numValue = parseFloat(value)
    const numExpected = parseFloat(rule.expectedValue)

    switch (rule.operator) {
      case ConditionOperatorType.Equal:
        return numValue === numExpected
      case ConditionOperatorType.NotEqual:
        return numValue !== numExpected
      case ConditionOperatorType.GreaterThan:
        return numValue > numExpected
      case ConditionOperatorType.LessThan:
        return numValue < numExpected
      default:
        return true
    }
  })
}))

jest.mock('@model/form/transform/common', () => ({
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  })
}))

// Mock isNumberValueMeetExpected before importing CurrencyFactory

import {CurrencyFactory} from '../CurrencyFactory'
import {
    CurrencyDefaultProps,
    CurrencyDefaultValue,
    CurrencyValue,
    FormElementCurrencyModel
} from '@model/form/defines/FormCurrency'
import {FormField} from '@model/form/defines/serverDataStructure'
import {FormErrorType} from '@model/form/defines/base'
import {FormChangeValueOption, FormElementCreateOption, FormElementTransformOption} from '../defines/shared'
import { ConditionRuleViewModel } from '@model/form/defines/condition'
import uuid from 'uuid/v4'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock the MxISDK module
jest.mock('isdk', () => ({
    MxISDK: {
        getCurrentUser: jest.fn(() => ({
            basicInfo: {
                timezone: 'America/Los_Angeles'
            }
        }))
    }
}))

// Mock browser environment
global.window = {
    parent: {},
    top: {},
} as any

// Mock moment-timezone
jest.mock('moment-timezone', () => ({
    tz: {
        guess: jest.fn(() => 'America/Los_Angeles')
    }
}))

describe('CurrencyFactory', () => {
    let currencyFactory: CurrencyFactory
    let mockFormField: FormField
    let mockCurrencyModel: FormElementCurrencyModel
    let mockCurrencyValue: CurrencyValue

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks()

        // Initialize the factory
        currencyFactory = new CurrencyFactory()

        // Create mock data for tests
        mockCurrencyValue = {
            amount: '100.00',
            code: 'USD'
        }

        mockCurrencyModel = {
            ...CurrencyDefaultProps,
            id: 'test-id',
            value: {...mockCurrencyValue},
            precision: 2,
            minLength: '0',
            maxLength: '1000',
            required: true
        }

        mockFormField = {
            id: 'test-id',
            name: 'test-currency',
            type: 'Currency',
            value: {...mockCurrencyValue},
            defaultValue: {...CurrencyDefaultValue},
            fieldSpecific: {
                precision: 2,
                minLength: '0',
                maxLength: '1000',
                hideLabel: false,
                isProtected: false,
                placeholder: '0.00',
                readonly: false,
                required: true,
                supporting: ''
            }
        }
    })

    describe('create', () => {
        it('should create a new currency model with default properties', () => {
            const option: FormElementCreateOption = {}
            const result = currencyFactory.create(option)

            expect(result).toEqual({
                ...CurrencyDefaultProps,
                id: 'mocked-uuid'
            })
            expect(uuid).toHaveBeenCalledTimes(1)
        })

        it('should process language resources if $t is provided', () => {
            const option: FormElementCreateOption = {
                $t: ($key: string) => `translated-${$key}`
            }
            const result = currencyFactory.create(option)

            // Since processLangResource is mocked, we can't test the exact behavior
            // but we can verify that the id is set correctly
            expect(result.id).toEqual('mocked-uuid')
        })
    })

    describe('toServerData', () => {
        it('should convert view model to server data', () => {
            const result = currencyFactory.toServerData(mockCurrencyModel)

            // Expect the result to contain all the properties from the model
            expect(result).toBeDefined()
            expect(result.id).toEqual(mockCurrencyModel.id)
            expect(result.value).toEqual(mockCurrencyModel.value)

            // Check that result.fieldSpecific has the required properties
            expect(result.fieldSpecific).toBeDefined()
            const requiredProps = [
                'precision',
                'minLength',
                'maxLength',
                'hideLabel',
                'isProtected',
                'placeholder',
                'readonly',
                'required',
                'supporting'
            ]
            requiredProps.forEach(prop => {
                expect(result.fieldSpecific).toHaveProperty(prop)
            })
        })
    })

    describe('toViewModel', () => {
        it('should convert server data to view model', () => {
            const result = currencyFactory.toViewModel(mockFormField)

            expect(result).toBeDefined()
            expect(result.id).toEqual(mockFormField.id)
            expect(result.value).toEqual(mockFormField.value)
            expect(result.precision).toEqual(mockFormField.fieldSpecific.precision)
            expect(result.minLength).toEqual(mockFormField.fieldSpecific.minLength)
            expect(result.maxLength).toEqual(mockFormField.fieldSpecific.maxLength)
        })

        it('should apply default value when option.applyDefaultValue is true and defaultValue exists', () => {
            const option: FormElementTransformOption = {
                applyDefaultValue: true
            }

            const result = currencyFactory.toViewModel(mockFormField, option)

            expect(result).toBeDefined()
            // In a real scenario, the useDefaultValue function would be called
            // and the value would be set to the defaultValue
        })
    })

    describe('validate', () => {
        it('should validate successfully when all required fields are filled', async () => {
            const validationPromise = currencyFactory.validate(mockCurrencyModel)

            await expect(validationPromise).resolves.toBe(true)
            expect(mockCurrencyModel.errors).toEqual([])
        })

        it('should fail validation when required field is missing', async () => {
            // Create a model with empty required field
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                value: {
                    ...mockCurrencyValue,
                    amount: '' // Empty amount
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Required
                })
            }
        })


        it('should not validate when required is false', async () => {
            // Create a model with required set to false
            const nonRequiredModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                required: false,
                value: {
                    ...CurrencyDefaultValue,
                    amount: '' // Empty amount
                }
            }

            await expect(currencyFactory.validate(nonRequiredModel)).resolves.toBe(true)
            expect(nonRequiredModel.errors).toEqual([])
        })

        it('should validate maxLength constraint', async () => {
            // Create a model with amount exceeding maxLength
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                maxLength: '100',
                value: {
                    ...mockCurrencyValue,
                    amount: '200' // Exceeds maxLength
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Limit
                })
            }
        })

        it('should validate minLength constraint', async () => {
            // Create a model with amount below minLength
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                minLength: '50',
                value: {
                    ...mockCurrencyValue,
                    amount: '10' // Below minLength
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Limit
                })
            }
        })

        it('should validate precision constraint with 2 decimal places', async () => {
            // Create a model with amount having too many decimal places
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                precision: 2,
                value: {
                    ...mockCurrencyValue,
                    amount: '100.123' // More than 2 decimal places
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Precision,
                    params: { precision: 2 }
                })
            }
        })

        it('should validate precision constraint with 1 decimal place', async () => {
            // Create a model with amount having too many decimal places
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                precision: 1,
                value: {
                    ...mockCurrencyValue,
                    amount: '100.12' // More than 1 decimal place
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Precision,
                    params: { precision: 1 }
                })
            }
        })

        it('should validate precision constraint with 0 decimal places', async () => {
            // Create a model with amount having decimal places when precision is 0
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                precision: 0,
                value: {
                    ...mockCurrencyValue,
                    amount: '100.1' // Has decimal places when precision is 0
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Precision,
                    params: { precision: 0 }
                })
            }
        })

        it('should validate multiple constraints simultaneously', async () => {
            // Create a model with multiple validation issues
            const invalidModel: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                minLength: '200',
                precision: 1,
                value: {
                    ...mockCurrencyValue,
                    amount: '100.12' // Below minLength and too many decimal places
                }
            }

            try {
                await currencyFactory.validate(invalidModel)
                fail('Validation should have failed')
            } catch (error) {
                expect(error).toBe(invalidModel)
                // Should have both Limit and Precision errors
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Limit
                })
                expect(invalidModel.errors).toContainEqual({
                    field: 'amount',
                    errorType: FormErrorType.Precision,
                    params: { precision: 1 }
                })
                // Should have exactly 2 errors
                expect(invalidModel.errors.length).toBe(2)
            }
        })

        it('should validate negative numbers correctly', async () => {
            // Create a model with a negative number
            const modelWithNegative: FormElementCurrencyModel = {
                ...mockCurrencyModel,
                minLength: '-200',
                maxLength: '200',
                value: {
                    ...mockCurrencyValue,
                    amount: '-100.00' // Negative number within range
                }
            }

            await expect(currencyFactory.validate(modelWithNegative)).resolves.toBe(true)
            expect(modelWithNegative.errors).toEqual([])
        })
    })

    describe('resetValue', () => {
        it('should reset the value to default', () => {
            currencyFactory.resetValue(mockCurrencyModel)

            expect(mockCurrencyModel.value).toEqual(CurrencyDefaultValue)
        })
    })

    describe('uiOption', () => {
        it('should return the correct UI options', () => {
            const result = currencyFactory.uiOption()

            expect(result).toEqual({
                view: 'FormCurrencyView',
                editor: 'FormCurrency'
            })
        })
    })

    describe('meetExpected', () => {
        it('should return true when amount meets the expected condition (equal)', () => {
            const rule: ConditionRuleViewModel = {
                elementId: 'test-id',
                label: 'Test Label',
                expectedValue: '100',
                operator: ConditionOperatorType.Equal,
                errors: []
            }

            const result = currencyFactory.meetExpected(mockCurrencyModel, rule)
            expect(result).toBe(true)
        })

        it('should return true when amount meets the expected condition (not equal)', () => {
            const rule: ConditionRuleViewModel = {
                elementId: 'test-id',
                label: 'Test Label',
                expectedValue: '200',
                operator: ConditionOperatorType.NotEqual,
                errors: []
            }

            const result = currencyFactory.meetExpected(mockCurrencyModel, rule)
            expect(result).toBe(true)
        })

        it('should return true when amount meets the expected condition (greater than)', () => {
            const rule: ConditionRuleViewModel = {
                elementId: 'test-id',
                label: 'Test Label',
                expectedValue: '50',
                operator: ConditionOperatorType.GreaterThan,
                errors: []
            }

            const result = currencyFactory.meetExpected(mockCurrencyModel, rule)
            expect(result).toBe(true)
        })

        it('should return true when amount meets the expected condition (less than)', () => {
            const rule: ConditionRuleViewModel = {
                elementId: 'test-id',
                label: 'Test Label',
                expectedValue: '200',
                operator: ConditionOperatorType.LessThan,
                errors: []
            }

            const result = currencyFactory.meetExpected(mockCurrencyModel, rule)
            expect(result).toBe(true)
        })

        it('should return false when amount does not meet the expected condition', () => {
            // Set the model value to be equal to expected value, so NotEqual should return false
            mockCurrencyModel.value.amount = '100'

            const rule: ConditionRuleViewModel = {
                elementId: 'test-id',
                label: 'Test Label',
                expectedValue: '100',
                operator: ConditionOperatorType.NotEqual,
                errors: []
            }

            const result = currencyFactory.meetExpected(mockCurrencyModel, rule)
            expect(result).toBe(false)
        })
    })

    describe('setValue', () => {
        it('should set the value correctly', () => {
            const newValue: CurrencyValue = {
                amount: '200.00',
                code: 'EUR'
            }

            const option: FormChangeValueOption = {}

            currencyFactory.setValue(mockCurrencyModel, newValue, option)

            expect(mockCurrencyModel.value).toEqual(newValue)
        })

        it('should set only the changed property when changedProperty is specified', () => {
            const originalValue = { ...mockCurrencyModel.value }
            const newValue: CurrencyValue = {
                ...originalValue,
                amount: '200.00'
            }

            const option: FormChangeValueOption = {
                changedProperty: 'amount'
            }

            currencyFactory.setValue(mockCurrencyModel, newValue, option)

            expect(mockCurrencyModel.value.amount).toEqual('200.00')
            // Code should remain unchanged
            expect(mockCurrencyModel.value.code).toEqual(originalValue.code)
        })
    })

    describe('syncValue', () => {
        it('should sync values correctly', () => {
            const newValue: CurrencyValue = {
                amount: '300.00',
                code: 'GBP'
            }

            const option: FormChangeValueOption = {
                focusedElementId: 'other-id', // Different from the model's id
                focusedProperty: undefined
            }

            currencyFactory.syncValue(mockCurrencyModel, newValue, option)

            // When the focused element is different, all values should be synced
            expect(mockCurrencyModel.value).toEqual({
                ...mockCurrencyModel.value,
                ...newValue
            })
        })

        it('should not sync the focused property when the model is the focused element', () => {
            const originalValue = { ...mockCurrencyModel.value }
            const newValue: CurrencyValue = {
                ...originalValue,
                amount: '400.00'
            }

            const option: FormChangeValueOption = {
                focusedElementId: 'test-id', // Same as the model's id
                focusedProperty: 'amount'
            }

            currencyFactory.syncValue(mockCurrencyModel, newValue, option)

            // The focused property should not be synced
            expect(mockCurrencyModel.value.amount).toEqual(originalValue.amount)
            // Other properties should be synced
            expect(mockCurrencyModel.value).toEqual(originalValue)
        })
    })
})
