// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  })
}))

import { EmailAddressFactory } from '../EmailAddressFactory'
import {
  EmailAddressDefaultProps,
  EmailAddressSpecialKeys,
  EmailAddressAlias,
  FormElementEmailAddressModel
} from '@model/form/defines/FormEmailAddress'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption } from '@model/form/defines/shared'
import uuid from 'uuid/v4'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock the processLangResource function
jest.mock('../shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

// Mock the syncFormElementSampleValue function
jest.mock('@model/form/factory/utils', () => ({
  syncFormElementSampleValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = value
    }
  })
}))

describe('EmailAddressFactory', () => {
  let emailAddressFactory: EmailAddressFactory
  let mockEmailAddressModel: FormElementEmailAddressModel
  let mockFormField: FormField

  beforeEach(() => {
    emailAddressFactory = new EmailAddressFactory()

    // Create a mock email address model for testing
    mockEmailAddressModel = {
      ...EmailAddressDefaultProps,
      id: 'test-id',
      label: 'Test Email',
      value: '<EMAIL>',
      defaultFromProfile: true,
      required: true
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'EmailAddress',
      label: 'Test Email',
      value: '<EMAIL>',
      defaultValue: '<EMAIL>',
      fieldSpecific: {
        defaultFromProfile: true, // This maps to enableAutoFill in the model
        placeholder: 'Enter your email',
        required: true,
        supporting: 'Supporting text',
        hideLabel: false,
        readonly: false
      }
    }
  })

  describe('create', () => {
    it('should create a new email address model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = emailAddressFactory.create(option)

      expect(result).toEqual({
        ...EmailAddressDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = emailAddressFactory.create(option)

      // Check that the label has been translated
      expect(result.label).toEqual('translated-label')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = emailAddressFactory.toServerData(mockEmailAddressModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockEmailAddressModel.id)
      expect(result.label).toEqual(mockEmailAddressModel.label)
      expect(result.value).toEqual(mockEmailAddressModel.value)
      // Check that defaultFromProfile is preserved
      expect(result.fieldSpecific.defaultFromProfile).toEqual(mockEmailAddressModel.defaultFromProfile)
      expect(result.fieldSpecific.required).toEqual(mockEmailAddressModel.required)
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = emailAddressFactory.toViewModel(mockFormField)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.label).toEqual(mockFormField.label)
      expect(result.value).toEqual(mockFormField.value)
      // Check that defaultFromProfile is preserved
      expect(result.defaultFromProfile).toEqual(mockFormField.fieldSpecific.defaultFromProfile)
      expect(result.required).toEqual(mockFormField.fieldSpecific.required)
      expect(result.placeholder).toEqual(mockFormField.fieldSpecific.placeholder)
      expect(result.supporting).toEqual(mockFormField.fieldSpecific.supporting)
    })

    it('should apply default value when option.applyDefaultValue is true and defaultValue exists', () => {
      const option: FormElementTransformOption = {
        applyDefaultValue: true,
        boardId: 'test-board',
        transactionSequence: 1
      }

      const result = emailAddressFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      // Note: EmailAddressFactory doesn't actually implement applyDefaultValue logic
      // so it will use the original value, not the defaultValue
      expect(result.value).toEqual(mockFormField.value)
    })
  })

  describe('validate', () => {
    it('should validate successfully when all required fields are filled', async () => {
      const validationPromise = emailAddressFactory.validate(mockEmailAddressModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockEmailAddressModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockEmailAddressModel.value = ''

      const validationPromise = emailAddressFactory.validate(mockEmailAddressModel)

      await expect(validationPromise).rejects.toEqual(mockEmailAddressModel)
      expect(mockEmailAddressModel.errors).toHaveLength(1)
      expect(mockEmailAddressModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty string', () => {
      mockEmailAddressModel.value = '<EMAIL>'

      emailAddressFactory.resetValue(mockEmailAddressModel)

      expect(mockEmailAddressModel.value).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = emailAddressFactory.uiOption()

      expect(result).toEqual({
        view: 'FormEmailAddressView',
        editor: 'FormEmailAddress'
      })
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {}
      
      emailAddressFactory.setValue(mockEmailAddressModel, '<EMAIL>', option)

      expect(mockEmailAddressModel.value).toEqual('<EMAIL>')
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const option = {
        focusedElementId: 'other-id'
      }
      
      emailAddressFactory.syncValue(mockEmailAddressModel, '<EMAIL>', option)

      expect(mockEmailAddressModel.value).toEqual('<EMAIL>')
    })

    it('should not sync the value when focused', () => {
      const option = {
        focusedElementId: 'test-id'
      }
      
      emailAddressFactory.syncValue(mockEmailAddressModel, '<EMAIL>', option)

      expect(mockEmailAddressModel.value).toEqual('<EMAIL>')
    })
  })
})
