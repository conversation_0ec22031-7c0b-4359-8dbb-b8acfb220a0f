import { SignatureFactory } from '../SignatureFactory'
import {
  SignatureDefaultProps,
  SignatureSpecialKeys,
  FormElementSignatureModel
} from '@model/form/defines/FormSignature'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => {
  if (Array.isArray(obj)) {
    return [...obj]
  } else if (typeof obj === 'object' && obj !== null) {
    return { ...obj }
  }
  return obj
}))

// Mock the processLangResource function
jest.mock('@model/form/factory/shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

// Mock getFieldSpecificFormViewModel
jest.mock('@model/form/transform/common', () => ({
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      if (model[key] !== undefined) {
        result[key] = model[key]
      }
    })
    return result
  })
}))

// Mock makeTransactionResourceUrl
jest.mock('@controller/contentLibrary/src/form', () => ({
  makeTransactionResourceUrl: jest.fn((boardId, transactionSequence, value, viewToken) => {
    return `https://example.com/resource/${boardId}/${transactionSequence}/${value}${viewToken ? `?token=${viewToken}` : ''}`
  })
}))

describe('SignatureFactory', () => {
  let signatureFactory: SignatureFactory
  let mockFormField: FormField
  let mockSignatureModel: FormElementSignatureModel

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Initialize the factory
    signatureFactory = new SignatureFactory()

    // Mock FormField data
    mockFormField = {
      id: 'test-id',
      type: 'Signature',
      uniqueName: 'test-unique-name',
      value: 'signature-file-uuid',
      name: 'Test name',
      label: 'Test label',
      fieldSpecific: {
        hideLabel: false,
        required: true,
        readonly: false
      }
    } as FormField

    // Mock SignatureModel
    mockSignatureModel = {
      ...SignatureDefaultProps,
      id: 'test-id',
      uniqueName: 'test-unique-name',
      value: 'signature-file-uuid',
      name: 'Test name',
      label: 'Test label',
      hideLabel: false,
      required: true,
      readonly: false,
      imageURL: 'https://example.com/resource/board-id/123/signature-file-uuid',
      errors: []
    }
  })

  describe('create', () => {
    it('should create a new signature model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = signatureFactory.create(option)

      expect(result).toEqual({
        ...SignatureDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(SignatureDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = signatureFactory.create(option)

      expect(require('@model/form/factory/shared').processLangResource).toHaveBeenCalledWith(expect.anything(), option.$t)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = signatureFactory.toServerData(mockSignatureModel)

      expect(result).toEqual({
        id: 'test-id',
        type: 'Signature',
        uniqueName: 'test-unique-name',
        name: 'Test name',
        value: 'signature-file-uuid',
        label: 'Test label',
        fieldSpecific: expect.any(Object)
      })
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {
        boardId: 'board-id',
        transactionSequence: 123,
        viewToken: 'view-token'
      }
      const result = signatureFactory.toViewModel(mockFormField, option)

      expect(result).toEqual({
        ...SignatureDefaultProps,
        id: 'test-id',
        uniqueName: 'test-unique-name',
        value: 'signature-file-uuid',
        name: 'Test name',
        label: 'Test label',
        hideLabel: false,
        required: true,
        readonly: false,
        imageURL: 'https://example.com/resource/board-id/123/signature-file-uuid?token=view-token'
      })
    })

    it('should not set imageURL when value is empty', () => {
      const fieldWithoutValue = {
        ...mockFormField,
        value: ''
      }
      const option: FormElementTransformOption = {
        boardId: 'board-id',
        transactionSequence: 123
      }
      const result = signatureFactory.toViewModel(fieldWithoutValue, option)

      expect(result.imageURL).toBeUndefined()
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      const validationPromise = signatureFactory.validate(mockSignatureModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSignatureModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockSignatureModel.value = ''

      const validationPromise = signatureFactory.validate(mockSignatureModel)

      await expect(validationPromise).rejects.toEqual(mockSignatureModel)
      expect(mockSignatureModel.errors).toHaveLength(1)
      expect(mockSignatureModel.errors[0].errorType).toEqual(FormErrorType.Required)
      expect(mockSignatureModel.errors[0].field).toEqual('value')
    })

    it('should validate successfully for template validation', async () => {
      const option: FormValidateOptions = { isValidateTemplate: true }
      mockSignatureModel.value = ''

      const validationPromise = signatureFactory.validate(mockSignatureModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSignatureModel.errors).toEqual([])
    })

    it('should validate successfully when not required and field is empty', async () => {
      mockSignatureModel.required = false
      mockSignatureModel.value = ''

      const validationPromise = signatureFactory.validate(mockSignatureModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSignatureModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty string', () => {
      signatureFactory.resetValue(mockSignatureModel)

      expect(mockSignatureModel.value).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for regular form', () => {
      const runtimeOption = { isPDFForm: false }
      const result = signatureFactory.uiOption(runtimeOption)

      expect(result).toEqual({
        view: 'FormSignatureView',
        editor: 'FormSignature'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const runtimeOption = { isPDFForm: true }
      const result = signatureFactory.uiOption(runtimeOption)

      expect(result).toEqual({
        view: 'PDFFormSignatureView',
        editor: ''
      })
    })
  })

  describe('setValue', () => {
    it('should set the value and imageURL when value is provided', () => {
      const option: FormChangeValueOption = {
        boardId: 'board-id',
        transactionSequence: 123,
        viewToken: 'view-token'
      }
      
      signatureFactory.setValue(mockSignatureModel, 'new-signature-uuid', option)

      expect(mockSignatureModel.value).toEqual('new-signature-uuid')
      expect(mockSignatureModel.imageURL).toEqual('https://example.com/resource/board-id/123/new-signature-uuid?token=view-token')
    })

    it('should clear value and imageURL when empty value is provided', () => {
      const option: FormChangeValueOption = {
        boardId: 'board-id',
        transactionSequence: 123
      }
      
      signatureFactory.setValue(mockSignatureModel, '', option)

      expect(mockSignatureModel.value).toEqual('')
      expect(mockSignatureModel.imageURL).toEqual('')
    })
  })

  describe('syncValue', () => {
    it('should call setValue with the same parameters', () => {
      const option: FormChangeValueOption = {
        boardId: 'board-id',
        transactionSequence: 123,
        viewToken: 'view-token'
      }
      
      const setValueSpy = jest.spyOn(signatureFactory, 'setValue')
      
      signatureFactory.syncValue(mockSignatureModel, 'new-signature-uuid', option)

      expect(setValueSpy).toHaveBeenCalledWith(mockSignatureModel, 'new-signature-uuid', option)
    })
  })
})
