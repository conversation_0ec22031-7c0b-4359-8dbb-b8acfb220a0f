import {DateFactory} from '../DateFactory'
import {
  DateDefaultProps,
  DateDefaultVaue,
  DateValue,
  FormElementDateModel
} from '@model/form/defines/FormDate'
import {FormField} from '@model/form/defines/serverDataStructure'
import {FormErrorType} from '@model/form/defines/base'
import {FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions} from '../defines/shared'
import uuid from 'uuid/v4'
import moment from 'moment/moment'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock the MxISDK module
jest.mock('isdk', () => ({
  MxISDK: {
    getCurrentUser: jest.fn(() => ({
      basicInfo: {
        timezone: 'America/Los_Angeles'
      }
    }))
  }
}))



// Mock moment and moment-timezone
jest.mock('moment/moment', () => {
  const actualMoment = jest.requireActual('moment/moment');
  const mockMoment = function(...args: any[]) {
    return actualMoment(...args);
  };

  // Copy all static methods from actual moment
  Object.assign(mockMoment, actualMoment);

  mockMoment.tz = jest.fn((date, format, timezone) => {
    // Handle different call signatures
    if (typeof format === 'string' && typeof timezone === 'string') {
      // moment.tz(dateStr, format, timezone)
      const m = actualMoment(date, format);
      return {
        valueOf: () => m.valueOf(),
        format: (fmt: string) => m.format(fmt)
      };
    } else if (typeof format === 'string') {
      // moment.tz(dateStr, timezone) - format is actually timezone
      // Use a proper date format to avoid warnings
      const m = actualMoment(date, 'MM/DD/YYYY');
      return {
        valueOf: () => m.valueOf(),
        format: (fmt: string) => m.format(fmt)
      };
    } else {
      // moment.tz(timestamp, timezone)
      const m = actualMoment(date);
      return {
        valueOf: () => m.valueOf(),
        format: (fmt: string) => m.format(fmt)
      };
    }
  });

  mockMoment.tz.guess = jest.fn(() => 'America/Los_Angeles');

  return mockMoment;
});

jest.mock('moment-timezone', () => ({
  tz: {
    guess: jest.fn(() => 'America/Los_Angeles')
  }
}))

// Mock isNumberValueMeetExpected
jest.mock('@model/form/common/condition', () => ({
  isNumberValueMeetExpected: jest.fn((rule, value) => {
    if (!value) return false

    const expectedValue = parseInt(rule.expectedValue)
    const numValue = parseInt(value)

    switch(rule.operator) {
      case '=':
        return numValue === expectedValue
      case '<>':
        return numValue !== expectedValue
      case '>':
        return numValue > expectedValue
      case '<':
        return numValue < expectedValue
      default:
        return false
    }
  })
}))

// Mock MxLogger
jest.mock('@commonUtils/MxLogger', () => ({
  MxLogger: {
    create: jest.fn(() => ({
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}))

describe('DateFactory', () => {
  let dateFactory: DateFactory
  let mockFormField: FormField
  let mockDateModel: FormElementDateModel
  let mockDateValue: DateValue
  let today: number
  let dateFormat: string

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Initialize the factory
    dateFactory = new DateFactory()

    // Get today's timestamp for testing
    today = moment().startOf('day').valueOf()
    dateFormat =  'DD/MMM/YYYY'
    // Create mock data for tests
    mockDateValue = {
      hour: 10,
      minute: 30,
      timeFormat: 'AM',
      timestamp: 0,
      dayTimestamp: 0,
      dateStr: moment(today).format(dateFormat)
    }

    mockDateModel = {
      ...DateDefaultProps,
      id: 'test-id',
      value: {...mockDateValue},
      dateFormat,
      withTime: true,
      enable24Hour: false,
      required: true
    }

    mockFormField = {
      id: 'test-id',
      name: 'test-date',
      type: 'Date',
      value: {...mockDateValue},
      defaultValue: {...DateDefaultVaue},
      fieldSpecific: {
        dateFormat,
        withTime: true,
        withDate: true,
        timeFormat: 'A',
        currentDate: false,
        currentTime: false,
        hideLabel: false,
        isProtected: false,
        readonly: false,
        required: true,
        supporting: ''
      }
    }
  })

  describe('create', () => {
    it('should create a new date model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = dateFactory.create(option)

      expect(result).toEqual({
        ...DateDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = dateFactory.create(option)

      // Since processLangResource is mocked, we can't test the exact behavior
      // but we can verify that the id is set correctly
      expect(result.id).toEqual('mocked-uuid')
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {

      const result = dateFactory.toServerData(mockDateModel, {})

      // Expect the result to contain all the properties from the model
      expect(result).toBeDefined()
      expect(result.id).toEqual(mockDateModel.id)
      expect(result.value.dateStr).toEqual(mockDateModel.value.dateStr)
      expect(result.fieldSpecific).toBeDefined()
      expect(result.displayValue).toBeDefined()
    })

    it('should convert timestamp from dateStr ', () => {

      const result = dateFactory.toServerData(mockDateModel, {})

      // Expect the result to contain all the properties from the model
      expect(result).toBeDefined()
      expect(result.id).toEqual(mockDateModel.id)

      // Get today's timestamp for testing - use a fixed date for consistency
      const testToday = new Date('2025-07-16').getTime()
      const timestamp = testToday + 10 * 60 * 60 * 1000 + 30 * 60 * 1000 // Today at 10:30 AM

      // Since our mock might not calculate exact timestamps, just check that they are numbers
      expect(typeof result.value.timestamp).toBe('number')
      expect(typeof result.value.dayTimestamp).toBe('number')


      // Check that result.fieldSpecific has the required properties
      expect(result.fieldSpecific).toBeDefined()
      expect(result.displayValue).toBeDefined()
    })

    it('should convert enable24Hour to timeFormat', () => {


      // Set enable24Hour to true
      mockDateModel.enable24Hour = true

      const result = dateFactory.toServerData(mockDateModel, {})
      // Check that timeFormat is set to '24'
      expect(result.fieldSpecific.timeFormat).toEqual('24')

    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {


      const result = dateFactory.toViewModel(mockFormField)
      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.value).toEqual(mockFormField.value)
      expect(result.dateFormat).toEqual(mockFormField.fieldSpecific.dateFormat)
      expect(result.withTime).toEqual(mockFormField.fieldSpecific.withTime)
    })

    it('should convert timeFormat to enable24Hour', () => {


      // Set timeFormat to '24'
      mockFormField.fieldSpecific.timeFormat = '24'

      const result = dateFactory.toViewModel(mockFormField)

      // Check that enable24Hour is set to true
      expect(result.enable24Hour).toEqual(true)
    })

    it('should apply default value when option.applyDefaultValue is true and defaultValue exists', () => {
      const option: FormElementTransformOption = {
        applyDefaultValue: true
      }

      const result = dateFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      // In a real scenario, the useDefaultValue function would be called
      // and the value would be set to the defaultValue
    })
  })

  describe('validate', () => {
    it('should validate successfully when all required fields are filled', async () => {
      const validationPromise = dateFactory.validate(mockDateModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockDateModel.errors).toEqual([])
    })

    it('should not validate when isValidateTemplate is true', async () => {
      const option: FormValidateOptions = {
        isValidateTemplate: true
      }

      const validationPromise = dateFactory.validate(mockDateModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockDateModel.errors).toEqual([])
    })

    it('should fail validation when required date field is missing', async () => {
      // Create a model with empty required date field
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        value: {
          ...mockDateValue,
          dateStr: '', // Empty date
          timestamp: null
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'date',
          errorType: FormErrorType.Required
        })
      }
    })

    it('should fail validation when required time fields are missing with withTime=true', async () => {
      // Create a model with empty required time fields
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        withTime: true,
        value: {
          ...mockDateValue,
          hour: null, // Empty hour
          minute: null // Empty minute
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'hour',
          errorType: FormErrorType.Required
        })
      }
    })

    it('should fail validation when only hour is provided with withTime=true', async () => {
      // Create a model with only hour provided
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        withTime: true,
        value: {
          ...mockDateValue,
          hour: 10,
          minute: null // Empty minute
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'minute',
          errorType: FormErrorType.Required
        })
        expect(invalidModel.errors).toContainEqual({
          field: 'minute',
          errorType: FormErrorType.InvalidTime
        })
      }
    })

    it('should fail validation when only minute is provided with withTime=true', async () => {
      // Create a model with only minute provided
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        withTime: true,
        value: {
          ...mockDateValue,
          hour: null, // Empty hour
          minute: 30
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'hour',
          errorType: FormErrorType.Required
        })
        expect(invalidModel.errors).toContainEqual({
          field: 'hour',
          errorType: FormErrorType.InvalidTime
        })
      }
    })

    it('should fail validation when hour is invalid in 12-hour format', async () => {
      // Create a model with invalid hour in 12-hour format
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        withTime: true,
        enable24Hour: false,
        value: {
          ...mockDateValue,
          hour: 13, // Invalid in 12-hour format (should be 1-12)
          minute: 30
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'hour',
          errorType: FormErrorType.InvalidTime
        })
      }
    })

    it('should fail validation when hour is invalid in 24-hour format', async () => {
      // Create a model with invalid hour in 24-hour format
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        withTime: true,
        enable24Hour: true,
        value: {
          ...mockDateValue,
          hour: 24, // Invalid in 24-hour format (should be 0-23)
          minute: 30
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'hour',
          errorType: FormErrorType.InvalidTime
        })
      }
    })

    it('should fail validation when minute is invalid', async () => {
      // Create a model with invalid minute
      const invalidModel: FormElementDateModel = {
        ...mockDateModel,
        withTime: true,
        value: {
          ...mockDateValue,
          hour: 10,
          minute: 61 // Invalid minute (should be 0-60)
        }
      }

      try {
        await dateFactory.validate(invalidModel)
        fail('Validation should have failed')
      } catch (error) {
        expect(error).toBe(invalidModel)
        expect(invalidModel.errors).toContainEqual({
          field: 'minute',
          errorType: FormErrorType.InvalidTime
        })
      }
    })

    it('should not validate time fields when withTime=false', async () => {
      // Create a model with withTime=false and invalid time fields
      const model: FormElementDateModel = {
        ...mockDateModel,
        withTime: false,
        value: {
          ...mockDateValue,
          hour: 24, // Invalid hour
          minute: 61 // Invalid minute
        }
      }

      // Should pass validation because withTime is false
      await expect(dateFactory.validate(model)).resolves.toBe(true)
      expect(model.errors).toEqual([])
    })

    it('should validate when required is false and withTime is false', async () => {
      // Create a model with required set to false
      const nonRequiredModel: FormElementDateModel = {
        ...mockDateModel,
        required: false,
        withTime: false, // Disable time validation
        value: {
          ...DateDefaultVaue // Empty date and time
        }
      }
      const result = await expect(dateFactory.validate(nonRequiredModel)).resolves.toBe(true)
      expect(nonRequiredModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to default', () => {
      dateFactory.resetValue(mockDateModel)

      expect(mockDateModel.value).toEqual(DateDefaultVaue)
      expect(mockDateModel.displayValue).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for normal form', () => {
      const result = dateFactory.uiOption({})

      expect(result).toEqual({
        view: 'FormDateView',
        editor: 'FormDateOptionView'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const result = dateFactory.uiOption({ isPDFForm: true })

      expect(result).toEqual({
        view: 'PDFFormDateView',
        editor: 'FormElementDateSetting'
      })
    })
  })

  // describe('meetExpected', () => {
  //   it('should return true when date meets the expected condition (equal)', () => {
  //     const rule: ConditionRuleViewModel = {
  //       elementId: 'test-id',
  //       label: 'Test Label',
  //       expectedValue: today.toString(),
  //       operator: ConditionOperatorType.Equal,
  //       errors: []
  //     }
  //
  //     const result = dateFactory.meetExpected(mockDateModel, rule)
  //     expect(result).toBe(true)
  //   })
  //
  //   it('should return true when date meets the expected condition (not equal)', () => {
  //     const yesterday = today - 24 * 60 * 60 * 1000
  //
  //     const rule: ConditionRuleViewModel = {
  //       elementId: 'test-id',
  //       label: 'Test Label',
  //       expectedValue: yesterday.toString(),
  //       operator: ConditionOperatorType.NotEqual,
  //       errors: []
  //     }
  //
  //     const result = dateFactory.meetExpected(mockDateModel, rule)
  //     expect(result).toBe(true)
  //   })
  //
  //   it('should return true when date meets the expected condition (greater than)', () => {
  //     const yesterday = today - 24 * 60 * 60 * 1000
  //
  //     const rule: ConditionRuleViewModel = {
  //       elementId: 'test-id',
  //       label: 'Test Label',
  //       expectedValue: yesterday.toString(),
  //       operator: ConditionOperatorType.GreaterThan,
  //       errors: []
  //     }
  //
  //     const result = dateFactory.meetExpected(mockDateModel, rule)
  //     expect(result).toBe(true)
  //   })
  //
  //   it('should return true when date meets the expected condition (less than)', () => {
  //     const tomorrow = today + 24 * 60 * 60 * 1000
  //
  //     const rule: ConditionRuleViewModel = {
  //       elementId: 'test-id',
  //       label: 'Test Label',
  //       expectedValue: tomorrow.toString(),
  //       operator: ConditionOperatorType.LessThan,
  //       errors: []
  //     }
  //
  //     const result = dateFactory.meetExpected(mockDateModel, rule)
  //     expect(result).toBe(true)
  //   })
  //
  //   it('should return false when date does not meet the expected condition', () => {
  //     const rule: ConditionRuleViewModel = {
  //       elementId: 'test-id',
  //       label: 'Test Label',
  //       expectedValue: today.toString(),
  //       operator: ConditionOperatorType.NotEqual,
  //       errors: []
  //     }
  //
  //     const result = dateFactory.meetExpected(mockDateModel, rule)
  //     expect(result).toBe(false)
  //   })
  //
  //   it('should return false when timestamp is null', () => {
  //     const modelWithNullTimestamp: FormElementDateModel = {
  //       ...mockDateModel,
  //       value: {
  //         ...mockDateValue,
  //         timestamp: null
  //       }
  //     }
  //
  //     const rule: ConditionRuleViewModel = {
  //       elementId: 'test-id',
  //       label: 'Test Label',
  //       expectedValue: today.toString(),
  //       operator: ConditionOperatorType.Equal,
  //       errors: []
  //     }
  //
  //     const result = dateFactory.meetExpected(modelWithNullTimestamp, rule)
  //     expect(result).toBe(false)
  //   })
  // })

  describe('setValue', () => {
    it('should throw error when changedProperty is not specified', () => {
      const newValue: DateValue = {
        ...mockDateValue,
        hour: 11,
        minute: 45
      }

      const option: FormChangeValueOption = {}

      expect(() => {
        dateFactory.setValue(mockDateModel, newValue, option)
      }).toThrow('You need to specify the property to be updated.')
    })

    it('should set the value correctly when changedProperty is specified', () => {
      // Mock moment.tz to return a predictable value
      const mockTimestamp = 1609459200000; // 2021-01-01 00:00:00
      const mockDayTimestamp = 1609459200000; // 2021-01-01 00:00:00

      // Override the moment.tz mock for this test
      (moment.tz as jest.Mock).mockImplementation(() => ({
        valueOf: () => mockTimestamp,
        format: () => '2021-01-01'
      }));

      const newValue: DateValue = {
        ...mockDateValue,
        hour: 11,
        minute: 45,
        timeFormat: 'AM'
      }

      const option: FormChangeValueOption = {
        changedProperty: 'hour'
      }

      dateFactory.setValue(mockDateModel, newValue, option)

      // The entire value object should be updated
      expect(mockDateModel.value.hour).toEqual(11)
      expect(mockDateModel.value.minute).toEqual(45)
      expect(mockDateModel.value.timeFormat).toEqual('AM')
      expect(mockDateModel.value.timestamp).toEqual(mockTimestamp)
      expect(mockDateModel.value.dayTimestamp).toEqual(mockTimestamp)
    })

    it('should calculate correct timestamp for 12-hour format', () => {
      // Mock moment.tz to return a predictable value
      const mockTimestamp = 1609459200000; // 2021-01-01 00:00:00

      // Override the moment.tz mock for this test
      (moment.tz as jest.Mock).mockImplementation(() => ({
        valueOf: () => mockTimestamp,
        format: () => '2021-01-01'
      }));

      const newValue: DateValue = {
        ...mockDateValue,
        hour: 11,
        minute: 45,
        timeFormat: 'PM'
      }

      const option: FormChangeValueOption = {
        changedProperty: 'hour'
      }

      dateFactory.setValue(mockDateModel, newValue, option)

      // The timestamp should be calculated correctly
      expect(mockDateModel.value.timestamp).toEqual(mockTimestamp)
    })

    it('should calculate correct timestamp for 24-hour format', () => {
      // Mock moment.tz to return a predictable value
      const mockTimestamp = 1609459200000; // 2021-01-01 00:00:00

      // Override the moment.tz mock for this test
      (moment.tz as jest.Mock).mockImplementation(() => ({
        valueOf: () => mockTimestamp,
        format: () => '2021-01-01'
      }));

      mockDateModel.enable24Hour = true

      const newValue: DateValue = {
        ...mockDateValue,
        hour: 23,
        minute: 45
      }

      const option: FormChangeValueOption = {
        changedProperty: 'hour'
      }

      dateFactory.setValue(mockDateModel, newValue, option)

      // The timestamp should be calculated correctly
      expect(mockDateModel.value.timestamp).toEqual(mockTimestamp)
    })
  })

  describe('syncValue', () => {
    it('should sync values correctly', () => {
      // Mock syncFormElementObjectValue
      jest.spyOn(require('@model/form/factory/utils'), 'syncFormElementObjectValue').mockImplementation((model, value, option) => {
        if (option.focusedElementId !== model.id) {
          model.value = {
            ...model.value,
            ...value
          }
        }
      })

      const newValue: DateValue = {
        ...mockDateValue,
        hour: 11,
        minute: 45,
        timeFormat: 'PM'
      }

      const option: FormChangeValueOption = {
        focusedElementId: 'other-id', // Different from the model's id
        focusedProperty: undefined
      }

      dateFactory.syncValue(mockDateModel, newValue, option)

      // When the focused element is different, all values should be synced
      expect(mockDateModel.value).toEqual({
        ...mockDateModel.value,
        ...newValue
      })
    })

    it('should not sync the focused property when the model is the focused element', () => {
      // Mock syncFormElementObjectValue
      jest.spyOn(require('@model/form/factory/utils'), 'syncFormElementObjectValue').mockImplementation((model, value, option) => {
        if (option.focusedElementId === model.id && option.focusedProperty) {
          // Don't sync the focused property
          const { [option.focusedProperty]: _, ...rest } = value
          model.value = {
            ...model.value,
            ...rest
          }
        } else {
          model.value = {
            ...model.value,
            ...value
          }
        }
      })

      const originalValue = { ...mockDateModel.value }
      const newValue: DateValue = {
        ...originalValue,
        hour: 11,
        minute: 45
      }

      const option: FormChangeValueOption = {
        focusedElementId: 'test-id', // Same as the model's id
        focusedProperty: 'hour'
      }

      dateFactory.syncValue(mockDateModel, newValue, option)

      // The focused property should not be synced
      expect(mockDateModel.value.hour).toEqual(originalValue.hour)
    })
  })
})
