// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { MultiSelectionFactory } from '../MultiSelectionFactory'
import {
  MultiSelectionDefaultProps,
  MultiSelectionSpecialKeys,
  FormElementMultiSelectionModel
} from '@model/form/defines/FormMultiSelection'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import { ConditionRuleViewModel, ConditionOperatorType } from '@model/form/defines/condition'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => Array.isArray(obj) ? [...obj] : { ...obj }))

// Mock the processLangResource function
jest.mock('../shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    // Simple mock implementation that replaces $t placeholders with translated values
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

// Mock the isArrayValueMeetExpected function
jest.mock('@model/form/common/condition', () => ({
  isArrayValueMeetExpected: jest.fn((rule, value) => {
    // Simple mock implementation for testing
    if (rule.operator === ConditionOperatorType.Equal) {
      return value.includes(rule.expectedValue)
    } else if (rule.operator === ConditionOperatorType.NotEqual) {
      return !value.includes(rule.expectedValue)
    }
    return false
  })
}))

// Mock the syncFormElementSampleValue function
jest.mock('@model/form/factory/utils', () => ({
  syncFormElementSampleValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = value
    }
  })
}))

// Mock the getAttrsFromViewModel and getAttrsFromFormField functions
jest.mock('@model/form/common/utils', () => ({
  getAttrsFromViewModel: jest.fn((model, defaultModel, specialKeys) => {
    // Simple mock implementation that returns a FormField
    return {
      id: model.id,
      type: model.type,
      value: model.value,
      defaultValue: model.defaultValue,
      label: model.label,
      displayValue: model.value?.join(', ') || '',
      fieldSpecific: {
        options: model.options,
        hideLabel: model.hideLabel,
        supporting: model.supporting,
        required: model.required
      }
    }
  }),
  getAttrsFromFormField: jest.fn((data, defaultModel, specialKeys, options) => {
    // Simple mock implementation that returns a FormElementMultiSelectionModel
    const model = {
      ...cloneDeep(defaultModel),
      id: data.id,
      type: data.type,
      value: data.value || [],
      defaultValue: data.defaultValue || [],
      label: data.label,
      options: data.fieldSpecific.options,
      hideLabel: data.fieldSpecific.hideLabel,
      supporting: data.fieldSpecific.supporting,
      required: data.fieldSpecific.required
    }

    // Apply transforms if provided
    if (options?.transforms?.value && data.defaultValue) {
      model.value = data.defaultValue
    }

    return model
  }),
  ModelAttrTransforms: jest.fn(),
  useDefaultValue: jest.fn((data) => data.defaultValue)
}))

describe('MultiSelectionFactory', () => {
  let multiSelectionFactory: MultiSelectionFactory
  let mockMultiSelectionModel: FormElementMultiSelectionModel
  let mockFormField: FormField

  beforeEach(() => {
    multiSelectionFactory = new MultiSelectionFactory()

    // Create a mock multi-selection model for testing
    mockMultiSelectionModel = {
      ...MultiSelectionDefaultProps,
      id: 'test-id',
      label: 'Test Multi-selection',
      value: ['Option 1', 'Option 3'],
      defaultValue: ['Option 2'],
      options: [
        { value: 'Option 1' },
        { value: 'Option 2' },
        { value: 'Option 3' }
      ],
      required: true,
      displayValue: 'Option 1, Option 3'
    }

    // Create a mock form field for testing
    mockFormField = {
      id: 'test-id',
      type: 'MultiSelection',
      label: 'Test Multi-selection',
      value: ['Option 1', 'Option 3'],
      defaultValue: ['Option 2'],
      displayValue: 'Option 1, Option 3',
      fieldSpecific: {
        options: [
          { value: 'Option 1' },
          { value: 'Option 2' },
          { value: 'Option 3' }
        ],
        hideLabel: false,
        supporting: 'Supporting text',
        required: true
      }
    }
  })

  describe('create', () => {
    it('should create a new multi-selection model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = multiSelectionFactory.create(option)

      expect(result).toEqual({
        ...MultiSelectionDefaultProps,
        value: [],
        defaultValue: [],
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(MultiSelectionDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = multiSelectionFactory.create(option)

      expect(require('../shared').processLangResource).toHaveBeenCalledWith(expect.anything(), option.$t)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = multiSelectionFactory.toServerData(mockMultiSelectionModel)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockMultiSelectionModel.id)
      expect(result.type).toEqual(mockMultiSelectionModel.type)
      expect(result.value).toEqual(mockMultiSelectionModel.value)
      expect(result.defaultValue).toEqual(mockMultiSelectionModel.defaultValue)
      expect(result.displayValue).toEqual('Option 1, Option 3')
      expect(result.fieldSpecific.options).toEqual(mockMultiSelectionModel.options)
      expect(result.fieldSpecific.required).toEqual(mockMultiSelectionModel.required)
    })

    it('should set displayValue to empty string when value is empty', () => {
      mockMultiSelectionModel.value = []
      
      const result = multiSelectionFactory.toServerData(mockMultiSelectionModel)

      expect(result.displayValue).toEqual('')
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {}
      const result = multiSelectionFactory.toViewModel(mockFormField, option)

      expect(result).toBeDefined()
      expect(result.id).toEqual(mockFormField.id)
      expect(result.type).toEqual(mockFormField.type)
      expect(result.value).toEqual(mockFormField.value)
      expect(result.defaultValue).toEqual(mockFormField.defaultValue)
      expect(result.options).toEqual(mockFormField.fieldSpecific.options)
      expect(result.required).toEqual(mockFormField.fieldSpecific.required)
    })

    it('should apply default value when option.applyDefaultValue is true', () => {
      const option: FormElementTransformOption = {
        applyDefaultValue: true
      }

      const result = multiSelectionFactory.toViewModel(mockFormField, option)

      expect(result.value).toEqual(mockFormField.defaultValue)
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      const validationPromise = multiSelectionFactory.validate(mockMultiSelectionModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockMultiSelectionModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockMultiSelectionModel.value = []

      const validationPromise = multiSelectionFactory.validate(mockMultiSelectionModel)

      await expect(validationPromise).rejects.toEqual(mockMultiSelectionModel)
      expect(mockMultiSelectionModel.errors).toHaveLength(1)
      expect(mockMultiSelectionModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })

    it('should validate template even if required field is empty', async () => {
      mockMultiSelectionModel.value = []
      const option: FormValidateOptions = {
        isValidateTemplate: true
      }

      const validationPromise = multiSelectionFactory.validate(mockMultiSelectionModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockMultiSelectionModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty array and clear errors', () => {
      mockMultiSelectionModel.value = ['Option 1', 'Option 3']
      mockMultiSelectionModel.errors = [{ field: '', errorType: FormErrorType.Required }]
      mockMultiSelectionModel.displayValue = 'Option 1, Option 3'

      multiSelectionFactory.resetValue(mockMultiSelectionModel)

      expect(mockMultiSelectionModel.value).toEqual([])
      expect(mockMultiSelectionModel.errors).toEqual([])
      expect(mockMultiSelectionModel.displayValue).toEqual('')
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for normal form', () => {
      const result = multiSelectionFactory.uiOption({})

      expect(result).toEqual({
        view: 'FormMultiSelectionView',
        editor: 'FormMultiSelection'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const result = multiSelectionFactory.uiOption({ isPDFForm: true })

      expect(result).toEqual({
        view: 'PDFFormMultiSelectionView',
        editor: 'PDFSelectionSetting'
      })
    })
  })

  describe('meetExpected', () => {
    it('should check if the value meets the expected condition (Equal)', () => {
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.Equal,
        expectedValue: 'Option 1'
      }

      const result = multiSelectionFactory.meetExpected(mockMultiSelectionModel, rule)

      expect(result).toBe(true)
      expect(require('@model/form/common/condition').isArrayValueMeetExpected).toHaveBeenCalledWith(rule, mockMultiSelectionModel.value)
    })

    it('should check if the value meets the expected condition (NotEqual)', () => {
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.NotEqual,
        expectedValue: 'Option 2'
      }

      const result = multiSelectionFactory.meetExpected(mockMultiSelectionModel, rule)

      expect(result).toBe(true)
      expect(require('@model/form/common/condition').isArrayValueMeetExpected).toHaveBeenCalledWith(rule, mockMultiSelectionModel.value)
    })
  })

  describe('setValue', () => {
    it('should set the value as a new array', () => {
      const newValue = ['Option 2', 'Option 3']
      const option: FormChangeValueOption = {}
      
      multiSelectionFactory.setValue(mockMultiSelectionModel, newValue, option)

      expect(mockMultiSelectionModel.value).toEqual(newValue)
      expect(cloneDeep).toHaveBeenCalledWith(newValue)
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const newValue = ['Option 2', 'Option 3']
      const option = {
        focusedElementId: 'other-id'
      }
      
      multiSelectionFactory.syncValue(mockMultiSelectionModel, newValue, option)

      expect(mockMultiSelectionModel.value).toEqual(newValue)
      expect(require('@model/form/factory/utils').syncFormElementSampleValue).toHaveBeenCalledWith(mockMultiSelectionModel, newValue, option)
    })

    it('should not sync the value when focused', () => {
      const newValue = ['Option 2', 'Option 3']
      const option = {
        focusedElementId: 'test-id'
      }
      
      multiSelectionFactory.syncValue(mockMultiSelectionModel, newValue, option)

      expect(mockMultiSelectionModel.value).toEqual(['Option 1', 'Option 3'])
      expect(require('@model/form/factory/utils').syncFormElementSampleValue).toHaveBeenCalledWith(mockMultiSelectionModel, newValue, option)
    })
  })
})
