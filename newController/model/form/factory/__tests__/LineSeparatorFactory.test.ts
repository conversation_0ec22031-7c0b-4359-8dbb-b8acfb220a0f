import { LineSeparatorFactory } from '../LineSeparatorFactory'
import {
  LineSeparatorDefaultProps,
  FormElementLineSeparatorModel
} from '@model/form/defines/FormLineSeparator'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormElementCreateOption, FormChangeValueOption, FormElementType } from '@model/form/defines/shared'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => {
  if (Array.isArray(obj)) {
    return [...obj]
  } else if (typeof obj === 'object' && obj !== null) {
    return { ...obj }
  }
  return obj
}))

describe('LineSeparatorFactory', () => {
  let lineSeparatorFactory: LineSeparatorFactory
  let mockFormField: FormField
  let mockLineSeparatorModel: FormElementLineSeparatorModel

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Initialize the factory
    lineSeparatorFactory = new LineSeparatorFactory()

    // Mock FormField data
    mockFormField = {
      id: 'test-id',
      type: 'LineSeparator',
      fieldSpecific: {}
    } as FormField

    // Mock LineSeparatorModel
    mockLineSeparatorModel = {
      ...LineSeparatorDefaultProps,
      id: 'test-id',
      type: FormElementType.LineSeparator,
      condition: null,
      errors: []
    }
  })

  describe('create', () => {
    it('should create a new line separator model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = lineSeparatorFactory.create(option)

      expect(result).toEqual({
        ...LineSeparatorDefaultProps,
        type: FormElementType.LineSeparator,
        id: 'mocked-uuid',
        condition: null,
        errors: []
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(LineSeparatorDefaultProps)
    })

    it('should not process language resources since LineSeparator does not use $t', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = lineSeparatorFactory.create(option)

      // LineSeparator doesn't process language resources, so the result should be the same
      expect(result).toEqual({
        ...LineSeparatorDefaultProps,
        type: FormElementType.LineSeparator,
        id: 'mocked-uuid',
        condition: null,
        errors: []
      })
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = lineSeparatorFactory.toServerData(mockLineSeparatorModel)

      expect(result).toEqual({
        id: 'test-id',
        type: FormElementType.LineSeparator,
        fieldSpecific: {}
      })
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = lineSeparatorFactory.toViewModel(mockFormField)

      expect(result).toEqual({
        ...LineSeparatorDefaultProps,
        id: 'test-id',
        type: 'LineSeparator',
        isVisible: true
      })
      expect(cloneDeep).toHaveBeenCalledWith(LineSeparatorDefaultProps)
    })
  })

  describe('validate', () => {
    it('should always validate successfully', async () => {
      const validationPromise = lineSeparatorFactory.validate(mockLineSeparatorModel)

      await expect(validationPromise).resolves.toBe(true)
    })

    it('should always validate successfully regardless of model state', async () => {
      // Modify the model to test that validation always passes
      mockLineSeparatorModel.isVisible = false
      
      const validationPromise = lineSeparatorFactory.validate(mockLineSeparatorModel)

      await expect(validationPromise).resolves.toBe(true)
    })
  })

  describe('resetValue', () => {
    it('should do nothing when resetValue is called', () => {
      const originalModel = { ...mockLineSeparatorModel }
      
      lineSeparatorFactory.resetValue(mockLineSeparatorModel)

      // Model should remain unchanged since resetValue does nothing
      expect(mockLineSeparatorModel).toEqual(originalModel)
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options', () => {
      const result = lineSeparatorFactory.uiOption()

      expect(result).toEqual({
        view: 'FormLineSeparatorView',
        editor: 'FormLineSeparator'
      })
    })
  })

  describe('setValue', () => {
    it('should do nothing when setValue is called', () => {
      const originalModel = { ...mockLineSeparatorModel }
      const option: FormChangeValueOption = {
        boardId: 'test-board',
        transactionSequence: 1
      }
      
      lineSeparatorFactory.setValue(mockLineSeparatorModel, 'some-value', option)

      // Model should remain unchanged since setValue does nothing
      expect(mockLineSeparatorModel).toEqual(originalModel)
    })
  })

  describe('syncValue', () => {
    it('should do nothing when syncValue is called', () => {
      const originalModel = { ...mockLineSeparatorModel }
      const option: FormChangeValueOption = {
        boardId: 'test-board',
        transactionSequence: 1
      }
      
      lineSeparatorFactory.syncValue(mockLineSeparatorModel, 'some-value', option)

      // Model should remain unchanged since syncValue does nothing
      expect(mockLineSeparatorModel).toEqual(originalModel)
    })
  })
})
