import { PageFactory } from '../PageFactory'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import { FormPage, FormField } from '@model/form/defines/serverDataStructure'
import { FormElementCreateOption, FormElementTransformOption, FormElementTransformToServerOption, FormValidateOptions, FormChangeValueOption, FormElementType, FormFactoryOption } from '@model/form/defines/shared'
import { FormPreprocessor } from '@model/form/factory/FormPreprocessor'
import { FormAnyElementViewModel, FormElementAnyInputModel } from '@model/form/defines/allType'
import uuid from 'uuid/v4'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock getFormElementFactory
const mockFactory = {
  toServerData: jest.fn((element) => ({
    id: element.id,
    type: element.type,
    value: element.value || '',
    fieldSpecific: {}
  })),
  toViewModel: jest.fn((data) => ({
    id: data.id,
    type: data.type,
    value: data.value || '',
    isVisible: true,
    errors: []
  })),
  validate: jest.fn((element) => {
    if (element.required && !element.value) {
      return Promise.reject(element)
    }
    return Promise.resolve(true)
  }),
  resetValue: jest.fn((element) => {
    element.value = ''
  })
}

jest.mock('@model/form/factory/shared', () => ({
  getFormElementFactory: jest.fn(() => mockFactory)
}))

// Mock getElementBaseViewModel
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  }))
}))

describe('PageFactory', () => {
  let pageFactory: PageFactory
  let mockPreprocessor: FormPreprocessor
  let mockFormPage: FormPage
  let mockPageModel: FormElementPageModel

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Mock preprocessor
    mockPreprocessor = {
      beforeElementToServerData: jest.fn(),
      afterElementToServerData: jest.fn(),
      beforeElementToViewModel: jest.fn(),
      afterElementToViewModel: jest.fn()
    } as FormPreprocessor

    // Initialize the factory with preprocessor
    const option: FormFactoryOption = {
      preprocessor: mockPreprocessor,
      type: FormElementType.Page
    }
    pageFactory = new PageFactory(option)

    // Mock FormPage data
    mockFormPage = {
      id: 'page-id',
      name: 'Test Page',
      type: FormElementType.Page,
      pageNumber: 1,
      fieldSpecific: {},
      fields: [
        {
          id: 'field-1',
          type: 'SingleLineText',
          uniqueName: 'field1',
          name: 'Field 1',
          value: 'Test value',
          fieldSpecific: {}
        } as FormField,
        {
          id: 'field-2',
          type: 'Number',
          uniqueName: 'field2',
          name: 'Field 2',
          value: '123',
          fieldSpecific: {}
        } as FormField
      ]
    }

    // Mock PageModel
    mockPageModel = {
      id: 'page-id',
      name: 'Test Page',
      type: FormElementType.Page,
      pageNumber: 1,
      label: '',
      defaultLabel: '',
      hideLabel: true,
      isVisible: true,
      errors: [],
      elements: [
        {
          id: 'field-1',
          type: 'SingleLineText',
          value: 'Test value',
          isVisible: true,
          required: false,
          errors: []
        } as FormAnyElementViewModel,
        {
          id: 'field-2',
          type: 'Number',
          value: '123',
          isVisible: true,
          required: true,
          errors: []
        } as FormAnyElementViewModel
      ]
    }
  })

  describe('constructor', () => {
    it('should initialize with preprocessor when option is provided', () => {
      const option: FormFactoryOption = {
        preprocessor: mockPreprocessor,
        type: FormElementType.Page
      }
      const factory = new PageFactory(option)
      
      expect(factory).toBeDefined()
    })

    it('should initialize without preprocessor when no option is provided', () => {
      const factory = new PageFactory()
      
      expect(factory).toBeDefined()
    })
  })

  describe('create', () => {
    it('should create a new page model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = pageFactory.create(option)

      expect(result).toEqual({
        id: 'mocked-uuid',
        name: '',
        type: FormElementType.Page,
        elements: [],
        pageNumber: 1,
        defaultLabel: '',
        label: '',
        hideLabel: true,
        isVisible: true,
        errors: []
      })
      expect(uuid).toHaveBeenCalledTimes(1)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const option: FormElementTransformToServerOption = {}
      const result = pageFactory.toServerData(mockPageModel, option)

      expect(result).toEqual({
        id: 'page-id',
        type: FormElementType.Page,
        pageNumber: 1,
        fields: [
          {
            id: 'field-1',
            type: 'SingleLineText',
            value: 'Test value',
            fieldSpecific: {}
          },
          {
            id: 'field-2',
            type: 'Number',
            value: '123',
            fieldSpecific: {}
          }
        ]
      })

      expect(mockFactory.toServerData).toHaveBeenCalledTimes(2)
      expect(mockPreprocessor.beforeElementToServerData).toHaveBeenCalledTimes(2)
      expect(mockPreprocessor.afterElementToServerData).toHaveBeenCalledTimes(2)
    })

    it('should handle empty elements array', () => {
      const emptyPageModel = {
        ...mockPageModel,
        elements: []
      }
      
      const result = pageFactory.toServerData(emptyPageModel)

      expect(result.fields).toEqual([])
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const option: FormElementTransformOption = {
        boardId: 'test-board',
        transactionSequence: 1
      }
      const result = pageFactory.toViewModel(mockFormPage, option)

      expect(result).toEqual({
        id: 'page-id',
        name: 'Test Page',
        isVisible: true,
        errors: [],
        type: FormElementType.Page,
        pageNumber: 1,
        label: '',
        defaultLabel: '',
        hideLabel: true,
        elements: [
          {
            id: 'field-1',
            type: 'SingleLineText',
            value: 'Test value',
            isVisible: true,
            errors: []
          },
          {
            id: 'field-2',
            type: 'Number',
            value: '123',
            isVisible: true,
            errors: []
          }
        ]
      })

      expect(mockFactory.toViewModel).toHaveBeenCalledTimes(2)
      expect(mockPreprocessor.beforeElementToViewModel).toHaveBeenCalledTimes(2)
      expect(mockPreprocessor.afterElementToViewModel).toHaveBeenCalledTimes(2)
    })
  })

  describe('validate', () => {
    it('should validate successfully when all elements are valid', async () => {
      const validationPromise = pageFactory.validate(mockPageModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockFactory.validate).toHaveBeenCalledTimes(2)
    })

    it('should reject when some elements are invalid', async () => {
      // Make the second element invalid by setting required but empty value
      mockPageModel.elements[1].value = ''
      mockPageModel.elements[1].required = true

      const validationPromise = pageFactory.validate(mockPageModel)

      await expect(validationPromise).rejects.toEqual([mockPageModel.elements[1]])
    })

    it('should resolve true when no elements are present', async () => {
      const emptyPageModel = {
        ...mockPageModel,
        elements: []
      }

      const validationPromise = pageFactory.validate(emptyPageModel)

      await expect(validationPromise).resolves.toBe(true)
    })

    it('should skip invisible elements during validation', async () => {
      mockPageModel.elements[0].isVisible = false

      const validationPromise = pageFactory.validate(mockPageModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockFactory.validate).toHaveBeenCalledTimes(1) // Only visible element validated
    })

    it('should pass validation options to element factories', async () => {
      const option: FormValidateOptions = { isValidateTemplate: true }

      await pageFactory.validate(mockPageModel, option)

      expect(mockFactory.validate).toHaveBeenCalledWith(mockPageModel.elements[0], option)
      expect(mockFactory.validate).toHaveBeenCalledWith(mockPageModel.elements[1], option)
    })
  })

  describe('resetValue', () => {
    it('should reset values for all elements', () => {
      pageFactory.resetValue(mockPageModel)

      expect(mockFactory.resetValue).toHaveBeenCalledTimes(2)
      expect(mockFactory.resetValue).toHaveBeenCalledWith(mockPageModel.elements[0])
      expect(mockFactory.resetValue).toHaveBeenCalledWith(mockPageModel.elements[1])
    })

    it('should handle empty elements array', () => {
      const emptyPageModel = {
        ...mockPageModel,
        elements: []
      }

      pageFactory.resetValue(emptyPageModel)

      expect(mockFactory.resetValue).not.toHaveBeenCalled()
    })
  })

  describe('uiOption', () => {
    it('should return empty UI options', () => {
      const result = pageFactory.uiOption()

      expect(result).toEqual({
        view: '',
        editor: ''
      })
    })
  })

  describe('setValue', () => {
    it('should do nothing when setValue is called', () => {
      const option: FormChangeValueOption = {
        boardId: 'test-board',
        transactionSequence: 1
      }
      
      // Should not throw any errors
      expect(() => {
        pageFactory.setValue(mockPageModel, 'some-value', option)
      }).not.toThrow()
    })
  })

  describe('syncValue', () => {
    it('should do nothing when syncValue is called', () => {
      const option: FormChangeValueOption = {
        boardId: 'test-board',
        transactionSequence: 1
      }

      // Should not throw any errors
      expect(() => {
        pageFactory.syncValue(mockPageModel, 'some-value', option)
      }).not.toThrow()
    })
  })
})
