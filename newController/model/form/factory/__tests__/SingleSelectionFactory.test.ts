// Mock problematic modules first
jest.mock('@model/form/transform/common', () => ({
  getElementBaseViewModel: jest.fn((data) => ({
    id: data.id,
    name: data.name || '',
    isVisible: true,
    errors: []
  })),
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      result[key] = model[key]
    })
    return result
  }),
  getElementBaseServerData: jest.fn((model) => ({
    id: model.id,
    type: model.type,
    name: model.name || '',
    fieldSpecific: model.fieldSpecific || {}
  }))
}))

import { SingleSelectionFactory } from '../SingleSelectionFactory'
import {
  SingleSelectionDefaultProps,
  SingleSelectionSpecialKeys,
  FormElementSingleSelectionModel
} from '@model/form/defines/FormSingleSelection'
import { FormField } from '@model/form/defines/serverDataStructure'
import { FormErrorType } from '@model/form/defines/base'
import { FormChangeValueOption, FormElementCreateOption, FormElementTransformOption, FormValidateOptions } from '@model/form/defines/shared'
import { ConditionRuleViewModel, ConditionOperatorType } from '@model/form/defines/condition'
import uuid from 'uuid/v4'
import cloneDeep from 'lodash/cloneDeep'

// Mock uuid to return a predictable value for testing
jest.mock('uuid/v4', () => jest.fn(() => 'mocked-uuid'))

// Mock cloneDeep to return a simple copy for testing
jest.mock('lodash/cloneDeep', () => jest.fn(obj => {
  if (Array.isArray(obj)) {
    return [...obj]
  } else if (typeof obj === 'object' && obj !== null) {
    return { ...obj }
  }
  return obj
}))

// Mock the processLangResource function
jest.mock('@model/form/factory/shared', () => ({
  processLangResource: jest.fn((model, $t) => {
    if (model.label && model.label.includes('$t(')) {
      model.label = 'translated-label'
    }
    if (model.placeholder && model.placeholder.includes('$t(')) {
      model.placeholder = 'translated-placeholder'
    }
  })
}))

// Mock syncFormElementSampleValue
jest.mock('@model/form/factory/utils', () => ({
  syncFormElementSampleValue: jest.fn((model, value, option) => {
    if (option.focusedElementId !== model.id) {
      model.value = value
    }
  })
}))

// Mock getFieldSpecificFormViewModel
jest.mock('@model/form/transform/common', () => ({
  getFieldSpecificFormViewModel: jest.fn((model, keys) => {
    const result = {}
    keys.forEach(key => {
      if (model[key] !== undefined) {
        result[key] = model[key]
      }
    })
    return result
  })
}))

// Mock isStringValueMeetExpected
jest.mock('@model/form/common/condition', () => ({
  isStringValueMeetExpected: jest.fn((rule, value) => {
    if (rule.operator === ConditionOperatorType.Equal) {
      return value === rule.expectedValue
    }
    return false
  })
}))

describe('SingleSelectionFactory', () => {
  let singleSelectionFactory: SingleSelectionFactory
  let mockFormField: FormField
  let mockSingleSelectionModel: FormElementSingleSelectionModel

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Initialize the factory
    singleSelectionFactory = new SingleSelectionFactory()

    // Mock FormField data
    mockFormField = {
      id: 'test-id',
      type: 'SingleSelection',
      uniqueName: 'test-unique-name',
      value: 'Option 1',
      name: 'Test name',
      label: 'Test label',
      defaultValue: 'Default option',
      fieldSpecific: {
        options: [{ value: 'Option 1' }, { value: 'Option 2' }],
        hideLabel: false,
        supporting: 'Test supporting text',
        required: true
      }
    } as FormField

    // Mock SingleSelectionModel
    mockSingleSelectionModel = {
      ...SingleSelectionDefaultProps,
      id: 'test-id',
      uniqueName: 'test-unique-name',
      value: 'Option 1',
      name: 'Test name',
      label: 'Test label',
      defaultValue: 'Default option',
      options: [{ value: 'Option 1' }, { value: 'Option 2' }],
      hideLabel: false,
      supporting: 'Test supporting text',
      required: true,
      errors: []
    }
  })

  describe('create', () => {
    it('should create a new single selection model with default properties', () => {
      const option: FormElementCreateOption = {}
      const result = singleSelectionFactory.create(option)

      expect(result).toEqual({
        ...SingleSelectionDefaultProps,
        id: 'mocked-uuid'
      })
      expect(uuid).toHaveBeenCalledTimes(1)
      expect(cloneDeep).toHaveBeenCalledWith(SingleSelectionDefaultProps)
    })

    it('should process language resources if $t is provided', () => {
      const option: FormElementCreateOption = {
        $t: ($key: string) => `translated-${$key}`
      }
      const result = singleSelectionFactory.create(option)

      expect(require('@model/form/factory/shared').processLangResource).toHaveBeenCalledWith(expect.anything(), option.$t)
    })
  })

  describe('toServerData', () => {
    it('should convert view model to server data', () => {
      const result = singleSelectionFactory.toServerData(mockSingleSelectionModel)

      expect(result).toEqual({
        id: 'test-id',
        type: 'SingleSelection',
        uniqueName: 'test-unique-name',
        value: 'Option 1',
        name: 'Test name',
        label: 'Test label',
        defaultValue: 'Default option',
        fieldSpecific: expect.any(Object)
      })
    })
  })

  describe('toViewModel', () => {
    it('should convert server data to view model', () => {
      const result = singleSelectionFactory.toViewModel(mockFormField)

      expect(result).toEqual({
        ...SingleSelectionDefaultProps,
        id: 'test-id',
        uniqueName: 'test-unique-name',
        value: 'Option 1',
        label: 'Test label',
        name: 'Test name',
        defaultValue: 'Default option',
        options: [{ value: 'Option 1' }, { value: 'Option 2' }],
        hideLabel: false,
        supporting: 'Test supporting text',
        required: true
      })
    })

    it('should apply default value when option.applyDefaultValue is true', () => {
      const option: FormElementTransformOption = { applyDefaultValue: true }
      const result = singleSelectionFactory.toViewModel(mockFormField, option)

      expect(result.value).toEqual('Default option')
    })
  })

  describe('validate', () => {
    it('should validate successfully when all requirements are met', async () => {
      const validationPromise = singleSelectionFactory.validate(mockSingleSelectionModel)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSingleSelectionModel.errors).toEqual([])
    })

    it('should not validate when required field is empty', async () => {
      mockSingleSelectionModel.value = ''

      const validationPromise = singleSelectionFactory.validate(mockSingleSelectionModel)

      await expect(validationPromise).rejects.toEqual(mockSingleSelectionModel)
      expect(mockSingleSelectionModel.errors).toHaveLength(1)
      expect(mockSingleSelectionModel.errors[0].errorType).toEqual(FormErrorType.Required)
    })

    it('should validate successfully for template validation', async () => {
      const option: FormValidateOptions = { isValidateTemplate: true }
      mockSingleSelectionModel.value = ''

      const validationPromise = singleSelectionFactory.validate(mockSingleSelectionModel, option)

      await expect(validationPromise).resolves.toBe(true)
      expect(mockSingleSelectionModel.errors).toEqual([])
    })
  })

  describe('resetValue', () => {
    it('should reset the value to empty string and clear errors', () => {
      mockSingleSelectionModel.errors = [{ field: '', errorType: FormErrorType.Required }]
      
      singleSelectionFactory.resetValue(mockSingleSelectionModel)

      expect(mockSingleSelectionModel.value).toEqual('')
      expect(mockSingleSelectionModel.errors).toEqual([])
    })
  })

  describe('uiOption', () => {
    it('should return the correct UI options for regular form', () => {
      const runtimeOption = { isPDFForm: false }
      const result = singleSelectionFactory.uiOption(runtimeOption)

      expect(result).toEqual({
        view: 'FormSingleSelectionView',
        editor: 'FormSingleSelection'
      })
    })

    it('should return the correct UI options for PDF form', () => {
      const runtimeOption = { isPDFForm: true }
      const result = singleSelectionFactory.uiOption(runtimeOption)

      expect(result).toEqual({
        view: 'PDFFormSingleSelectionView',
        editor: 'PDFSelectionSetting'
      })
    })
  })

  describe('meetExpected', () => {
    it('should return true when condition is met', () => {
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.Equal,
        expectedValue: 'Option 1'
      }

      const result = singleSelectionFactory.meetExpected(mockSingleSelectionModel, rule)

      expect(result).toBe(true)
    })

    it('should return false when condition is not met', () => {
      const rule: ConditionRuleViewModel = {
        operator: ConditionOperatorType.Equal,
        expectedValue: 'Option 3'
      }

      const result = singleSelectionFactory.meetExpected(mockSingleSelectionModel, rule)

      expect(result).toBe(false)
    })
  })

  describe('setValue', () => {
    it('should set the value', () => {
      const option: FormChangeValueOption = {}
      
      singleSelectionFactory.setValue(mockSingleSelectionModel, 'Option 2', option)

      expect(mockSingleSelectionModel.value).toEqual('Option 2')
    })
  })

  describe('syncValue', () => {
    it('should sync the value when not focused', () => {
      const option = {
        focusedElementId: 'other-id'
      }
      
      singleSelectionFactory.syncValue(mockSingleSelectionModel, 'Option 2', option)

      expect(mockSingleSelectionModel.value).toEqual('Option 2')
    })

    it('should not sync the value when focused', () => {
      const option = {
        focusedElementId: 'test-id'
      }

      singleSelectionFactory.syncValue(mockSingleSelectionModel, 'Option 2', option)

      expect(mockSingleSelectionModel.value).toEqual('Option 1')
    })
  })
})
