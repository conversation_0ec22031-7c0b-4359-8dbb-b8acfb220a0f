import { FormElementAllType, FormElementId } from '@model/form/defines/shared'
import { ElementConditionViewModel } from '@model/form/defines/condition'

interface FormElementBaseModel {
  id: FormElementId;
  type: FormElementAllType;
  name: string;
  condition?: ElementConditionViewModel;
  label: string;
  hideLabel: boolean;
  defaultLabel: string;
}

const enum FormErrorType {
  Required = 'required',
  InvalidTime = 'invalidTime',
  Limit = 'limit',
  Precision = 'precision',
  AnswerLimit = 'answerLimit',
  PhoneNumber = 'phoneNumber',
  AnswerMaxLimit = 'answerMaxLimit',
  AnswerMinLimit = 'answerMinLimit',
  MinLimit = 'minLimit',
  MaxLimit = 'maxLimit',
}

interface FormElementError {
  field: string;
  errorType: FormErrorType;
  params?: Record<string, any>;
}

/**
 * for UI logic
 * if you want to use language resource, can set '$t("currency")' as value
 **/

interface FormElementBaseUIProps {
  isVisible: boolean;
  errors: FormElementError[];
}

/**
 * The recorded attributes are ultimately stored in the fieldSpecific property
 */
const FormElementBaseProps = ['id', 'type', 'value', 'defaultValue']

export interface FormElementCustomModel {
  width: number;
  height: number;
  x: number;
  y: number;
  fontSize?: string;
  letterSpacing?: string;
  numberOfCells?: string;
}

export interface FormElementSelectionOption extends Partial<FormElementCustomModel> {
  label?: string;
  value: string;
  pdfFormFieldId?: string;
  exportValue?: string;
}

interface FormElementInputBaseModel<T> extends FormElementBaseModel {
  value: T;
  defaultValue: T;
  pdfFormFieldId?: string;
  label: string;
  uniqueName: string;
  hideLabel: boolean;
  supporting: string;
  required: boolean;
  placeholder: string;
  isProtected: boolean;
  readonly: boolean;
  defaultLabel: string;
  defaultPlaceholder?: string;
  errors?: FormElementError[];
  customData?: FormElementCustomModel;
}


const InputElementSpecialKeys = ['hideLabel', 'supporting', 'required', 'placeholder', 'isProtected', 'readonly']

export type { FormElementBaseModel, FormElementInputBaseModel, FormElementBaseUIProps, FormElementError }
export {
  FormElementBaseProps, InputElementSpecialKeys, FormErrorType
}